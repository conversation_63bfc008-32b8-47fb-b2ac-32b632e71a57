/**
 * 场景导出服务
 * 处理场景的保存、加载和导出功能
 */
import * as THREE from 'three';
import { SceneData } from './SceneLoader';

export interface ExportOptions {
  format: 'gltf' | 'glb' | 'json';
  includeTextures: boolean;
  includeMaterials: boolean;
  includeAnimations: boolean;
  includeLights: boolean;
  includeCameras: boolean;
  optimizeGeometry: boolean;
  compressTextures: boolean;
  embedTextures: boolean;
}

export interface ProjectData {
  id: string;
  name: string;
  description: string;
  version: string;
  scenes: SceneReference[];
  assets: AssetReference[];
  settings: ProjectSettings;
  createdAt: Date;
  updatedAt: Date;
}

export interface SceneReference {
  id: string;
  name: string;
  path: string;
  thumbnail?: string;
  isDefault?: boolean;
}

export interface AssetReference {
  id: string;
  name: string;
  type: string;
  path: string;
  size?: number;
}

export interface ProjectSettings {
  rendering: {
    shadows: boolean;
    postProcessing: boolean;
    antiAliasing: boolean;
  };
  physics: {
    enabled: boolean;
    gravity: number;
  };
  audio: {
    enabled: boolean;
    volume: number;
  };
}

class SceneExportService {
  private static instance: SceneExportService;

  public static getInstance(): SceneExportService {
    if (!SceneExportService.instance) {
      SceneExportService.instance = new SceneExportService();
    }
    return SceneExportService.instance;
  }

  /**
   * 导出场景为GLTF格式
   */
  public async exportSceneAsGLTF(
    scene: THREE.Scene,
    options: Partial<ExportOptions> = {}
  ): Promise<Blob | null> {
    try {
      console.log('开始导出场景为GLTF格式');
      
      const exportOptions: ExportOptions = {
        format: 'gltf',
        includeTextures: true,
        includeMaterials: true,
        includeAnimations: true,
        includeLights: true,
        includeCameras: true,
        optimizeGeometry: false,
        compressTextures: false,
        embedTextures: false,
        ...options
      };

      // 这里应该使用Three.js的GLTFExporter
      // 暂时创建一个模拟的GLTF数据
      const gltfData = this.createMockGLTFData(scene, exportOptions);
      
      const jsonString = JSON.stringify(gltfData, null, 2);
      const blob = new Blob([jsonString], { type: 'application/json' });
      
      console.log('场景导出完成');
      return blob;
    } catch (error) {
      console.error('导出场景失败:', error);
      return null;
    }
  }

  /**
   * 创建模拟的GLTF数据
   */
  private createMockGLTFData(scene: THREE.Scene, options: ExportOptions): any {
    const gltfData = {
      asset: {
        version: '2.0',
        generator: 'DL Engine Editor',
        copyright: 'DL Engine Project'
      },
      scene: 0,
      scenes: [
        {
          name: scene.name || 'Scene',
          nodes: []
        }
      ],
      nodes: [],
      meshes: [],
      materials: [],
      textures: [],
      images: [],
      accessors: [],
      bufferViews: [],
      buffers: []
    };

    // 遍历场景对象并转换为GLTF格式
    let nodeIndex = 0;
    scene.children.forEach(child => {
      if (child.visible) {
        const nodeData = this.convertObjectToGLTFNode(child, nodeIndex++, options);
        if (nodeData) {
          gltfData.nodes.push(nodeData);
          gltfData.scenes[0].nodes.push(nodeData.index);
        }
      }
    });

    return gltfData;
  }

  /**
   * 将Three.js对象转换为GLTF节点
   */
  private convertObjectToGLTFNode(object: THREE.Object3D, index: number, options: ExportOptions): any {
    const node: any = {
      name: object.name || `Node_${index}`,
      index: index
    };

    // 添加变换信息
    if (!object.position.equals(new THREE.Vector3(0, 0, 0))) {
      node.translation = object.position.toArray();
    }
    if (!object.quaternion.equals(new THREE.Quaternion(0, 0, 0, 1))) {
      node.rotation = object.quaternion.toArray();
    }
    if (!object.scale.equals(new THREE.Vector3(1, 1, 1))) {
      node.scale = object.scale.toArray();
    }

    // 处理网格对象
    if (object instanceof THREE.Mesh && options.includeMaterials) {
      // 这里应该添加网格和材质的处理逻辑
      node.mesh = index; // 简化处理
    }

    // 处理光源
    if (object instanceof THREE.Light && options.includeLights) {
      // 这里应该添加光源的处理逻辑
      node.extensions = {
        KHR_lights_punctual: {
          light: index
        }
      };
    }

    // 处理相机
    if (object instanceof THREE.Camera && options.includeCameras) {
      node.camera = index;
    }

    return node;
  }

  /**
   * 保存场景数据
   */
  public async saveSceneData(sceneData: SceneData, projectId: string): Promise<boolean> {
    try {
      console.log('保存场景数据:', sceneData.name);
      
      // 这里应该将场景数据保存到服务器或本地存储
      // 暂时保存到localStorage
      const sceneKey = `scene_${projectId}_${sceneData.id}`;
      const sceneJson = JSON.stringify(sceneData);
      localStorage.setItem(sceneKey, sceneJson);
      
      console.log('场景数据保存成功');
      return true;
    } catch (error) {
      console.error('保存场景数据失败:', error);
      return false;
    }
  }

  /**
   * 加载场景数据
   */
  public async loadSceneData(sceneId: string, projectId: string): Promise<SceneData | null> {
    try {
      console.log('加载场景数据:', sceneId);
      
      // 从localStorage加载场景数据
      const sceneKey = `scene_${projectId}_${sceneId}`;
      const sceneJson = localStorage.getItem(sceneKey);
      
      if (sceneJson) {
        const sceneData = JSON.parse(sceneJson) as SceneData;
        console.log('场景数据加载成功');
        return sceneData;
      } else {
        console.warn('未找到场景数据');
        return null;
      }
    } catch (error) {
      console.error('加载场景数据失败:', error);
      return null;
    }
  }

  /**
   * 导出项目数据
   */
  public async exportProjectData(projectData: ProjectData): Promise<Blob | null> {
    try {
      console.log('导出项目数据:', projectData.name);
      
      const projectJson = JSON.stringify(projectData, null, 2);
      const blob = new Blob([projectJson], { type: 'application/json' });
      
      console.log('项目数据导出完成');
      return blob;
    } catch (error) {
      console.error('导出项目数据失败:', error);
      return null;
    }
  }

  /**
   * 导入项目数据
   */
  public async importProjectData(file: File): Promise<ProjectData | null> {
    try {
      console.log('导入项目数据:', file.name);
      
      const text = await file.text();
      const projectData = JSON.parse(text) as ProjectData;
      
      // 验证项目数据格式
      if (!this.validateProjectData(projectData)) {
        throw new Error('无效的项目数据格式');
      }
      
      console.log('项目数据导入成功');
      return projectData;
    } catch (error) {
      console.error('导入项目数据失败:', error);
      return null;
    }
  }

  /**
   * 验证项目数据格式
   */
  private validateProjectData(data: any): data is ProjectData {
    return (
      data &&
      typeof data.id === 'string' &&
      typeof data.name === 'string' &&
      Array.isArray(data.scenes) &&
      Array.isArray(data.assets) &&
      data.settings &&
      typeof data.settings === 'object'
    );
  }

  /**
   * 创建场景缩略图
   */
  public async generateSceneThumbnail(
    renderer: THREE.WebGLRenderer,
    scene: THREE.Scene,
    camera: THREE.Camera,
    width: number = 256,
    height: number = 256
  ): Promise<string | null> {
    try {
      // 保存当前渲染器状态
      const originalSize = renderer.getSize(new THREE.Vector2());
      
      // 设置缩略图尺寸
      renderer.setSize(width, height);
      
      // 渲染场景
      renderer.render(scene, camera);
      
      // 获取canvas数据
      const canvas = renderer.domElement;
      const dataURL = canvas.toDataURL('image/png');
      
      // 恢复原始尺寸
      renderer.setSize(originalSize.x, originalSize.y);
      
      console.log('场景缩略图生成成功');
      return dataURL;
    } catch (error) {
      console.error('生成场景缩略图失败:', error);
      return null;
    }
  }

  /**
   * 下载文件
   */
  public downloadFile(blob: Blob, filename: string): void {
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }

  /**
   * 获取场景统计信息
   */
  public getSceneStats(scene: THREE.Scene): {
    objectCount: number;
    meshCount: number;
    lightCount: number;
    cameraCount: number;
    materialCount: number;
    textureCount: number;
  } {
    let objectCount = 0;
    let meshCount = 0;
    let lightCount = 0;
    let cameraCount = 0;
    const materials = new Set();
    const textures = new Set();

    scene.traverse((object) => {
      objectCount++;
      
      if (object instanceof THREE.Mesh) {
        meshCount++;
        if (object.material) {
          if (Array.isArray(object.material)) {
            object.material.forEach(mat => materials.add(mat.uuid));
          } else {
            materials.add(object.material.uuid);
          }
        }
      } else if (object instanceof THREE.Light) {
        lightCount++;
      } else if (object instanceof THREE.Camera) {
        cameraCount++;
      }
    });

    // 统计纹理（简化处理）
    materials.forEach((material: any) => {
      if (material.map) textures.add(material.map.uuid);
      if (material.normalMap) textures.add(material.normalMap.uuid);
      if (material.roughnessMap) textures.add(material.roughnessMap.uuid);
      if (material.metalnessMap) textures.add(material.metalnessMap.uuid);
    });

    return {
      objectCount,
      meshCount,
      lightCount,
      cameraCount,
      materialCount: materials.size,
      textureCount: textures.size
    };
  }
}

export default SceneExportService;
