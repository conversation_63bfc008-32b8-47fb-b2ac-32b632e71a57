/**
 * 面板标签定义 - 参考原项目ir-engine-dev的面板标签结构
 */
import React, { Suspense } from 'react';
import { TabData } from 'rc-dock';
import {
  ViewportPanelTitle,
  HierarchyPanelTitle,
  AssetsPanelTitle,
  ScenePanelTitle,
  InspectorPanelTitle,
  PropertiesPanelTitle,
  MaterialsPanelTitle,
  ConsolePanelTitle,
  VisualScriptPanelTitle
} from './PanelComponents';

// 导入面板组件
import ViewportPanel from './ViewportPanel';
import HierarchyPanel from './HierarchyPanel';
import AssetsPanel from './AssetsPanel';
import EnhancedAssetsPanel from './EnhancedAssetsPanel';
import ScenePanel from './ScenePanel';
import InspectorPanel from './InspectorPanel';
import ConsolePanel from './ConsolePanel';

// 错误边界组件
const ErrorBoundary: React.FC<{ fallback: React.ReactNode; children: React.ReactNode }> = ({ 
  fallback, 
  children 
}) => {
  try {
    return <>{children}</>;
  } catch (error) {
    console.error('Panel error:', error);
    return <>{fallback}</>;
  }
};

// 视口面板标签
export const ViewportPanelTab: TabData = {
  id: 'viewPanel',
  closable: false,
  title: <ViewportPanelTitle />,
  content: (
    <ErrorBoundary fallback={<div>Error occurred with the Viewport tab</div>}>
      <Suspense fallback={<div>Loading Viewport...</div>}>
        <ViewportPanel />
      </Suspense>
    </ErrorBoundary>
  )
};

// 层级面板标签
export const HierarchyPanelTab: TabData = {
  id: 'hierarchyPanel',
  closable: false,
  title: <HierarchyPanelTitle />,
  content: (
    <ErrorBoundary fallback={<div>Error occurred with the Hierarchy tab</div>}>
      <Suspense fallback={<div>Loading Hierarchy...</div>}>
        <HierarchyPanel />
      </Suspense>
    </ErrorBoundary>
  )
};

// 资源面板标签
export const AssetsPanelTab: TabData = {
  id: 'assetsPanel',
  closable: false,
  title: <AssetsPanelTitle />,
  content: (
    <ErrorBoundary fallback={<div>Error occurred with the Assets tab</div>}>
      <Suspense fallback={<div>Loading Assets...</div>}>
        <EnhancedAssetsPanel />
      </Suspense>
    </ErrorBoundary>
  )
};

// 场景面板标签
export const ScenePanelTab: TabData = {
  id: 'scenePanel',
  closable: true,
  title: <ScenePanelTitle />,
  content: (
    <ErrorBoundary fallback={<div>Error occurred with the Scene tab</div>}>
      <Suspense fallback={<div>Loading Scene...</div>}>
        <ScenePanel />
      </Suspense>
    </ErrorBoundary>
  )
};

// 检查器面板标签
export const InspectorPanelTab: TabData = {
  id: 'inspectorPanel',
  closable: false,
  title: <InspectorPanelTitle />,
  content: (
    <ErrorBoundary fallback={<div>Error occurred with the Inspector tab</div>}>
      <Suspense fallback={<div>Loading Inspector...</div>}>
        <InspectorPanel />
      </Suspense>
    </ErrorBoundary>
  )
};

// 属性面板标签
export const PropertiesPanelTab: TabData = {
  id: 'propertiesPanel',
  closable: false,
  title: <PropertiesPanelTitle />,
  content: (
    <ErrorBoundary fallback={<div>Error occurred with the Properties tab</div>}>
      <Suspense fallback={<div>Loading Properties...</div>}>
        <InspectorPanel /> // 暂时使用InspectorPanel
      </Suspense>
    </ErrorBoundary>
  )
};

// 材质面板标签
export const MaterialsPanelTab: TabData = {
  id: 'materialsPanel',
  closable: true,
  title: <MaterialsPanelTitle />,
  content: (
    <ErrorBoundary fallback={<div>Error occurred with the Materials tab</div>}>
      <Suspense fallback={<div>Loading Materials...</div>}>
        <div style={{ padding: '16px', color: '#fff' }}>
          <h3>材质库</h3>
          <p>材质面板内容待实现</p>
        </div>
      </Suspense>
    </ErrorBoundary>
  )
};

// 控制台面板标签
export const ConsolePanelTab: TabData = {
  id: 'consolePanel',
  closable: true,
  title: <ConsolePanelTitle />,
  content: (
    <ErrorBoundary fallback={<div>Error occurred with the Console tab</div>}>
      <Suspense fallback={<div>Loading Console...</div>}>
        <ConsolePanel />
      </Suspense>
    </ErrorBoundary>
  )
};

// 可视化脚本面板标签
export const VisualScriptPanelTab: TabData = {
  id: 'visualScriptPanel',
  closable: true,
  title: <VisualScriptPanelTitle />,
  content: (
    <ErrorBoundary fallback={<div>Error occurred with the Visual Script tab</div>}>
      <Suspense fallback={<div>Loading Visual Script...</div>}>
        <div style={{ padding: '16px', color: '#fff' }}>
          <h3>可视化脚本</h3>
          <p>可视化脚本面板内容待实现</p>
        </div>
      </Suspense>
    </ErrorBoundary>
  )
};

// 导出所有面板标签
export const AllPanelTabs = {
  ViewportPanelTab,
  HierarchyPanelTab,
  AssetsPanelTab,
  ScenePanelTab,
  InspectorPanelTab,
  PropertiesPanelTab,
  MaterialsPanelTab,
  ConsolePanelTab,
  VisualScriptPanelTab
};
