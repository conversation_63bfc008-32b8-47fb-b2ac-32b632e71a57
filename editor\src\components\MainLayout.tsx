/**
 * 编辑器主布局组件
 * 使用rc-dock库实现可停靠的面板布局
 */
import React, { useState, useEffect } from 'react';
import { Layout, Menu, Button, Dropdown, Modal } from 'antd';
import type { MenuProps } from 'antd';
import {
  MenuUnfoldOutlined,
  MenuFoldOutlined,
  AppstoreOutlined,
  SettingOutlined,
  QuestionCircleOutlined,
  BookOutlined,
  VideoCameraOutlined,
  UserOutlined,
  GlobalOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { DockLayout } from 'rc-dock';
import 'rc-dock/dist/rc-dock.css';
import { useTranslation } from 'react-i18next';

import {
  ViewportPanelTab,
  HierarchyPanelTab,
  AssetsPanelTab,
  ScenePanelTab,
  InspectorPanelTab,
  PropertiesPanelTab,
  MaterialsPanelTab,
  ConsolePanelTab,
  VisualScriptPanelTab
} from './panels/PanelTabs';
import HelpPanel from './panels/HelpPanel';
import TutorialPanel from './tutorials/TutorialPanel';

import './MainLayout.less';

const { Header, Sider, Content } = Layout;

interface MainLayoutProps {
  // 可以添加属性
}

const MainLayout: React.FC<MainLayoutProps> = () => {
  const { t, i18n } = useTranslation();
  const [collapsed, setCollapsed] = useState(false);
  const [helpVisible, setHelpVisible] = useState(false);
  const [tutorialVisible, setTutorialVisible] = useState(false);
  const [currentLanguage, setCurrentLanguage] = useState(i18n.language);

  // 监听语言变化
  useEffect(() => {
    setCurrentLanguage(i18n.language);
  }, [i18n.language]);
  
  // 初始化停靠布局配置 - 参考原项目ir-engine-dev的布局结构
  const defaultLayout = {
    dockbox: {
      mode: 'horizontal' as const,
      children: [
        {
          mode: 'vertical' as const,
          size: 8, // 左侧主要区域，占比更大
          children: [
            {
              tabs: [ViewportPanelTab]
            },
            {
              tabs: [AssetsPanelTab, VisualScriptPanelTab]
            }
          ]
        },
        {
          mode: 'vertical' as const,
          size: 3, // 右侧面板区域
          children: [
            {
              tabs: [HierarchyPanelTab, ScenePanelTab, MaterialsPanelTab]
            },
            {
              tabs: [PropertiesPanelTab, InspectorPanelTab],
              activeId: 'propertiesPanel' // 默认激活属性面板
            }
          ]
        }
      ]
    }
  };

  const [layout] = useState(defaultLayout);
  
  // 切换侧边栏折叠状态
  const toggleCollapsed = () => {
    setCollapsed(!collapsed);
  };
  
  // 切换帮助面板可见性
  const toggleHelp = () => {
    setHelpVisible(!helpVisible);
  };
  
  // 切换教程面板可见性
  const toggleTutorial = () => {
    setTutorialVisible(!tutorialVisible);
  };
  
  // 切换语言
  const changeLanguage = (lang: string) => {
    i18n.changeLanguage(lang);
    setCurrentLanguage(lang);
  };
  
  // 语言菜单
  const languageMenuItems: MenuProps['items'] = [
    {
      key: 'zh-CN',
      label: '中文',
      onClick: () => changeLanguage('zh-CN')
    },
    {
      key: 'en-US',
      label: 'English',
      onClick: () => changeLanguage('en-US')
    }
  ];

  // 帮助菜单
  const helpMenuItems: MenuProps['items'] = [
    {
      key: 'help',
      label: (
        <>
          <QuestionCircleOutlined /> {t('menu.help')}
        </>
      ),
      onClick: toggleHelp
    },
    {
      key: 'tutorials',
      label: (
        <>
          <VideoCameraOutlined /> {t('menu.tutorials')}
        </>
      ),
      onClick: toggleTutorial
    },
    {
      key: 'documentation',
      label: (
        <>
          <BookOutlined /> {t('menu.documentation')}
        </>
      )
    },
    {
      type: 'divider'
    },
    {
      key: 'about',
      label: (
        <>
          <InfoCircleOutlined /> {t('menu.about')}
        </>
      )
    }
  ];
  
  return (
    <Layout className="main-layout">
      <Header className="header">
        <div className="logo">IR Editor</div>
        <Menu theme="dark" mode="horizontal" defaultSelectedKeys={['1']}>
          <Menu.Item key="1">{t('menu.file')}</Menu.Item>
          <Menu.Item key="2">{t('menu.edit')}</Menu.Item>
          <Menu.Item key="3">{t('menu.view')}</Menu.Item>
          <Menu.Item key="4">{t('menu.window')}</Menu.Item>
        </Menu>
        <div className="header-right">
          <Dropdown menu={{ items: languageMenuItems }} placement="bottomRight">
            <Button type="text" icon={<GlobalOutlined />} style={{ color: 'white' }}>
              {currentLanguage === 'zh-CN' ? '中文' : 'English'}
            </Button>
          </Dropdown>
          <Dropdown menu={{ items: helpMenuItems }} placement="bottomRight">
            <Button type="text" icon={<QuestionCircleOutlined />} style={{ color: 'white' }} />
          </Dropdown>
          <Button type="text" icon={<UserOutlined />} style={{ color: 'white' }} />
        </div>
      </Header>
      <Layout>
        <Sider trigger={null} collapsible collapsed={collapsed} width={200}>
          <Button 
            type="text" 
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={toggleCollapsed}
            style={{ margin: '16px', color: 'white' }}
          />
          <Menu theme="dark" mode="inline" defaultSelectedKeys={['1']}>
            <Menu.Item key="1" icon={<AppstoreOutlined />}>
              {t('menu.project')}
            </Menu.Item>
            <Menu.Item key="2" icon={<SettingOutlined />}>
              {t('menu.settings')}
            </Menu.Item>
          </Menu>
        </Sider>
        <Content className="content">
          <DockLayout
            defaultLayout={layout}
            style={{ position: 'absolute', left: 0, top: 0, right: 0, bottom: 0 }}
          />
        </Content>
      </Layout>
      
      {/* 帮助面板模态框 */}
      <Modal
        title={t('help.title')}
        open={helpVisible}
        onCancel={toggleHelp}
        footer={null}
        width={800}
        styles={{ body: { height: '600px', padding: 0, overflow: 'hidden' } }}
      >
        <HelpPanel />
      </Modal>

      {/* 教程面板模态框 */}
      <Modal
        title={t('tutorials.title')}
        open={tutorialVisible}
        onCancel={toggleTutorial}
        footer={null}
        width={800}
        styles={{ body: { height: '600px', padding: 0, overflow: 'hidden' } }}
      >
        <TutorialPanel onClose={toggleTutorial} />
      </Modal>
    </Layout>
  );
};

export default MainLayout;
