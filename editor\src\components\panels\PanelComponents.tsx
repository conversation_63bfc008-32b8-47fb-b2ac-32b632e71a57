/**
 * 面板组件 - 参考原项目ir-engine-dev的面板结构
 */
import React, { ReactNode } from 'react';
import { useTranslation } from 'react-i18next';

interface PanelDragContainerProps {
  children: ReactNode;
  dataTestId?: string;
}

/**
 * 面板拖拽容器组件
 */
export const PanelDragContainer: React.FC<PanelDragContainerProps> = ({ 
  children, 
  dataTestId 
}) => {
  return (
    <div 
      data-testid={dataTestId}
      style={{
        display: 'flex',
        alignItems: 'center',
        height: '100%',
        cursor: 'grab'
      }}
      onMouseDown={(e) => {
        // 这里可以添加拖拽逻辑
        e.currentTarget.style.cursor = 'grabbing';
      }}
      onMouseUp={(e) => {
        e.currentTarget.style.cursor = 'grab';
      }}
    >
      {children}
    </div>
  );
};

interface PanelTitleProps {
  children: ReactNode;
}

/**
 * 面板标题组件
 */
export const PanelTitle: React.FC<PanelTitleProps> = ({ children }) => {
  return (
    <span style={{
      color: '#fff',
      fontSize: '14px',
      fontWeight: 500,
      userSelect: 'none'
    }}>
      {children}
    </span>
  );
};

// 各个面板的标题组件
export const ViewportPanelTitle = () => {
  const { t } = useTranslation();
  
  return (
    <PanelDragContainer dataTestId="viewport-panel-tab">
      <PanelTitle>{t('editor.viewport') || '视口'}</PanelTitle>
    </PanelDragContainer>
  );
};

export const HierarchyPanelTitle = () => {
  const { t } = useTranslation();
  
  return (
    <PanelDragContainer dataTestId="hierarchy-panel-tab">
      <PanelTitle>{t('editor.hierarchy') || '层级'}</PanelTitle>
    </PanelDragContainer>
  );
};

export const AssetsPanelTitle = () => {
  const { t } = useTranslation();
  
  return (
    <PanelDragContainer dataTestId="assets-panel-tab">
      <PanelTitle>{t('editor.assets') || '资源'}</PanelTitle>
    </PanelDragContainer>
  );
};

export const ScenePanelTitle = () => {
  const { t } = useTranslation();
  
  return (
    <PanelDragContainer dataTestId="scene-panel-tab">
      <PanelTitle>{t('editor.scene') || '场景'}</PanelTitle>
    </PanelDragContainer>
  );
};

export const InspectorPanelTitle = () => {
  const { t } = useTranslation();
  
  return (
    <PanelDragContainer dataTestId="inspector-panel-tab">
      <PanelTitle>{t('editor.inspector') || '检查器'}</PanelTitle>
    </PanelDragContainer>
  );
};

export const PropertiesPanelTitle = () => {
  const { t } = useTranslation();
  
  return (
    <PanelDragContainer dataTestId="properties-panel-tab">
      <PanelTitle>{t('editor.properties') || '属性'}</PanelTitle>
    </PanelDragContainer>
  );
};

export const MaterialsPanelTitle = () => {
  const { t } = useTranslation();
  
  return (
    <PanelDragContainer dataTestId="materials-panel-tab">
      <PanelTitle>{t('editor.materials') || '材质'}</PanelTitle>
    </PanelDragContainer>
  );
};

export const ConsolePanelTitle = () => {
  const { t } = useTranslation();
  
  return (
    <PanelDragContainer dataTestId="console-panel-tab">
      <PanelTitle>{t('editor.console') || '控制台'}</PanelTitle>
    </PanelDragContainer>
  );
};

export const VisualScriptPanelTitle = () => {
  const { t } = useTranslation();
  
  return (
    <PanelDragContainer dataTestId="visualscript-panel-tab">
      <PanelTitle>{t('editor.visualScript') || '可视化脚本'}</PanelTitle>
    </PanelDragContainer>
  );
};
