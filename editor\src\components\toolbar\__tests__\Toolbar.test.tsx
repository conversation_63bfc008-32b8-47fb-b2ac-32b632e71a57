/**
 * 工具栏组件测试
 */
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
import { <PERSON>rowserRouter } from 'react-router-dom';
import { configureStore } from '@reduxjs/toolkit';
import Toolbar from '../Toolbar';
import editorReducer, { TransformMode, TransformSpace } from '../../../store/editor/editorSlice';
import uiReducer from '../../../store/ui/uiSlice';

// 创建测试用的store
const createTestStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      editor: editorReducer,
      ui: uiReducer,
    },
    preloadedState: {
      editor: {
        isLoading: false,
        error: null,
        activeCamera: null,
        selectedObject: null,
        selectedObjects: [],
        transformMode: TransformMode.TRANSLATE,
        transformSpace: TransformSpace.LOCAL,
        snapMode: 'disabled',
        gridSize: 1,
        showGrid: true,
        showAxes: true,
        showStats: false,
        undoStack: [],
        redoStack: [],
        isPlaying: false,
        viewportSize: { width: 0, height: 0 },
        sceneGraph: [],
        ...initialState.editor,
      },
      ui: {
        panels: [],
        dialogs: [],
        theme: 'dark',
        language: 'zh-CN',
        sidebarCollapsed: false,
        fullscreen: false,
        menuVisible: true,
        viewportMode: 'select',
        renderMode: 'textured',
        contextMenu: {
          isOpen: false,
          x: 0,
          y: 0,
          items: [],
        },
        notifications: [],
        layout: null,
        savedLayouts: {},
        activeLayout: 'default',
        ...initialState.ui,
      },
    },
  });
};

// 测试组件包装器
const TestWrapper: React.FC<{ children: React.ReactNode; store?: any }> = ({ 
  children, 
  store = createTestStore() 
}) => (
  <Provider store={store}>
    <BrowserRouter>
      {children}
    </BrowserRouter>
  </Provider>
);

describe('Toolbar Component', () => {
  beforeEach(() => {
    // 模拟 ThreeRenderService
    jest.mock('../../../services/ThreeRenderService', () => ({
      getInstance: () => ({
        setTransformMode: jest.fn(),
        setTransformSpace: jest.fn(),
        resetCamera: jest.fn(),
        focusOnSelected: jest.fn(),
        getScene: () => ({}),
      }),
    }));
  });

  test('renders toolbar with all main sections', () => {
    render(
      <TestWrapper>
        <Toolbar />
      </TestWrapper>
    );

    // 检查Logo是否存在
    expect(screen.getByText('IR')).toBeInTheDocument();
    
    // 检查主要按钮组是否存在
    expect(screen.getByRole('button', { name: /撤销/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /重做/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /选择/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /移动/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /旋转/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /缩放/i })).toBeInTheDocument();
  });

  test('transform mode buttons work correctly', () => {
    const store = createTestStore();
    render(
      <TestWrapper store={store}>
        <Toolbar />
      </TestWrapper>
    );

    // 点击选择工具
    const selectButton = screen.getByRole('button', { name: /选择/i });
    fireEvent.click(selectButton);

    // 检查状态是否更新
    const state = store.getState();
    expect(state.editor.transformMode).toBe(TransformMode.SELECT);
  });

  test('transform space buttons work correctly', () => {
    const store = createTestStore();
    render(
      <TestWrapper store={store}>
        <Toolbar />
      </TestWrapper>
    );

    // 点击世界空间按钮
    const worldSpaceButton = screen.getByRole('button', { name: /世界空间/i });
    fireEvent.click(worldSpaceButton);

    // 检查状态是否更新
    const state = store.getState();
    expect(state.editor.transformSpace).toBe(TransformSpace.WORLD);
  });

  test('display options toggle correctly', () => {
    const store = createTestStore({
      editor: { showGrid: true, showAxes: true }
    });
    
    render(
      <TestWrapper store={store}>
        <Toolbar />
      </TestWrapper>
    );

    // 点击网格显示按钮
    const gridButton = screen.getByRole('button', { name: /隐藏网格/i });
    fireEvent.click(gridButton);

    // 检查状态是否更新
    let state = store.getState();
    expect(state.editor.showGrid).toBe(false);

    // 点击坐标轴显示按钮
    const axesButton = screen.getByRole('button', { name: /隐藏坐标轴/i });
    fireEvent.click(axesButton);

    // 检查状态是否更新
    state = store.getState();
    expect(state.editor.showAxes).toBe(false);
  });

  test('play/pause button works correctly', () => {
    const store = createTestStore({
      editor: { isPlaying: false }
    });
    
    render(
      <TestWrapper store={store}>
        <Toolbar />
      </TestWrapper>
    );

    // 点击播放按钮
    const playButton = screen.getByRole('button', { name: /播放/i });
    fireEvent.click(playButton);

    // 检查状态是否更新
    const state = store.getState();
    expect(state.editor.isPlaying).toBe(true);
  });

  test('fullscreen button works correctly', () => {
    const store = createTestStore({
      ui: { fullscreen: false }
    });
    
    render(
      <TestWrapper store={store}>
        <Toolbar />
      </TestWrapper>
    );

    // 点击全屏按钮
    const fullscreenButton = screen.getByRole('button', { name: /全屏/i });
    fireEvent.click(fullscreenButton);

    // 检查状态是否更新
    const state = store.getState();
    expect(state.ui.fullscreen).toBe(true);
  });

  test('undo/redo buttons work correctly', () => {
    const store = createTestStore({
      editor: { 
        undoStack: [{ type: 'test', data: {} }],
        redoStack: []
      }
    });
    
    render(
      <TestWrapper store={store}>
        <Toolbar />
      </TestWrapper>
    );

    // 点击撤销按钮
    const undoButton = screen.getByRole('button', { name: /撤销/i });
    fireEvent.click(undoButton);

    // 检查撤销栈是否更新
    let state = store.getState();
    expect(state.editor.undoStack).toHaveLength(0);
    expect(state.editor.redoStack).toHaveLength(1);

    // 点击重做按钮
    const redoButton = screen.getByRole('button', { name: /重做/i });
    fireEvent.click(redoButton);

    // 检查重做栈是否更新
    state = store.getState();
    expect(state.editor.undoStack).toHaveLength(1);
    expect(state.editor.redoStack).toHaveLength(0);
  });

  test('breadcrumb navigation displays correctly', () => {
    render(
      <TestWrapper>
        <Toolbar />
      </TestWrapper>
    );

    // 检查面包屑导航是否显示项目和场景名称
    expect(screen.getByText('Demo Project')).toBeInTheDocument();
    expect(screen.getByText('Main Scene')).toBeInTheDocument();
  });

  test('publish button is present', () => {
    render(
      <TestWrapper>
        <Toolbar />
      </TestWrapper>
    );

    // 检查发布按钮是否存在
    expect(screen.getByRole('button', { name: /发布/i })).toBeInTheDocument();
  });
});
