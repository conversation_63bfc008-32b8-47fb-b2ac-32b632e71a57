/**
 * Three.js 性能优化服务
 * 专门针对Three.js场景的性能优化和监控
 */
import * as THREE from 'three';

export interface ThreePerformanceStats {
  fps: number;
  frameTime: number;
  drawCalls: number;
  triangles: number;
  geometries: number;
  textures: number;
  memoryUsage: {
    geometries: number;
    textures: number;
    total: number;
  };
}

export interface ThreeOptimizationSettings {
  enableLOD: boolean;
  enableFrustumCulling: boolean;
  enableInstancing: boolean;
  enableBatching: boolean;
  maxDrawCalls: number;
  targetFPS: number;
  lodDistances: number[];
  autoOptimize: boolean;
}

class ThreePerformanceService {
  private static instance: ThreePerformanceService;
  
  private renderer: THREE.WebGLRenderer | null = null;
  private scene: THREE.Scene | null = null;
  private camera: THREE.Camera | null = null;
  
  // 性能监控
  private stats: ThreePerformanceStats = {
    fps: 0,
    frameTime: 0,
    drawCalls: 0,
    triangles: 0,
    geometries: 0,
    textures: 0,
    memoryUsage: {
      geometries: 0,
      textures: 0,
      total: 0
    }
  };
  
  // 优化设置
  private settings: ThreeOptimizationSettings = {
    enableLOD: true,
    enableFrustumCulling: true,
    enableInstancing: true,
    enableBatching: false, // 默认关闭，因为可能影响编辑功能
    maxDrawCalls: 1000,
    targetFPS: 60,
    lodDistances: [10, 50, 100, 200],
    autoOptimize: false
  };
  
  // 性能监控
  private frameCount = 0;
  private lastTime = 0;
  private fpsUpdateInterval = 1000; // 1秒更新一次FPS
  
  // 事件监听器
  private statsUpdateListeners: ((stats: ThreePerformanceStats) => void)[] = [];

  public static getInstance(): ThreePerformanceService {
    if (!ThreePerformanceService.instance) {
      ThreePerformanceService.instance = new ThreePerformanceService();
    }
    return ThreePerformanceService.instance;
  }

  /**
   * 初始化性能服务
   */
  public initialize(
    renderer: THREE.WebGLRenderer,
    scene: THREE.Scene,
    camera: THREE.Camera
  ): void {
    this.renderer = renderer;
    this.scene = scene;
    this.camera = camera;
    
    // 优化渲染器设置
    this.optimizeRenderer();
    
    // 开始性能监控
    this.startPerformanceMonitoring();
    
    console.log('Three.js 性能服务初始化完成');
  }

  /**
   * 优化渲染器设置
   */
  private optimizeRenderer(): void {
    if (!this.renderer) return;

    // 设置合适的像素比
    this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    
    // 启用色彩管理
    this.renderer.outputEncoding = THREE.sRGBEncoding;
    this.renderer.physicallyCorrectLights = true;
    
    // 优化阴影设置
    if (this.renderer.shadowMap.enabled) {
      this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
      this.renderer.shadowMap.autoUpdate = false; // 手动更新阴影以提高性能
    }
    
    console.log('渲染器优化完成');
  }

  /**
   * 开始性能监控
   */
  private startPerformanceMonitoring(): void {
    const monitor = () => {
      this.updatePerformanceStats();
      requestAnimationFrame(monitor);
    };
    monitor();
  }

  /**
   * 更新性能统计
   */
  private updatePerformanceStats(): void {
    const currentTime = performance.now();
    this.frameCount++;
    
    // 计算FPS
    if (currentTime - this.lastTime >= this.fpsUpdateInterval) {
      this.stats.fps = Math.round((this.frameCount * 1000) / (currentTime - this.lastTime));
      this.stats.frameTime = (currentTime - this.lastTime) / this.frameCount;
      this.frameCount = 0;
      this.lastTime = currentTime;
      
      // 更新其他统计信息
      this.updateRenderStats();
      
      // 通知监听器
      this.statsUpdateListeners.forEach(listener => listener(this.stats));
      
      // 自动优化检查
      if (this.settings.autoOptimize && this.stats.fps < this.settings.targetFPS) {
        this.performAutoOptimization();
      }
    }
  }

  /**
   * 更新渲染统计
   */
  private updateRenderStats(): void {
    if (!this.renderer) return;
    
    const info = this.renderer.info;
    this.stats.drawCalls = info.render.calls;
    this.stats.triangles = info.render.triangles;
    this.stats.geometries = info.memory.geometries;
    this.stats.textures = info.memory.textures;
    
    this.stats.memoryUsage.geometries = info.memory.geometries;
    this.stats.memoryUsage.textures = info.memory.textures;
    this.stats.memoryUsage.total = this.stats.memoryUsage.geometries + this.stats.memoryUsage.textures;
  }

  /**
   * 执行自动优化
   */
  private performAutoOptimization(): void {
    if (!this.scene) return;
    
    console.log('执行自动优化，当前FPS:', this.stats.fps);
    
    // 如果绘制调用过多，尝试启用批处理
    if (this.stats.drawCalls > this.settings.maxDrawCalls && !this.settings.enableBatching) {
      console.log('启用批处理优化');
      this.settings.enableBatching = true;
    }
    
    // 如果三角形数量过多，启用LOD
    if (this.stats.triangles > 100000 && !this.settings.enableLOD) {
      console.log('启用LOD优化');
      this.settings.enableLOD = true;
    }
    
    // 启用视锥剔除
    if (!this.settings.enableFrustumCulling) {
      console.log('启用视锥剔除');
      this.settings.enableFrustumCulling = true;
    }
  }

  /**
   * 应用视锥剔除
   */
  public applyFrustumCulling(): void {
    if (!this.scene || !this.camera || !this.settings.enableFrustumCulling) return;
    
    const frustum = new THREE.Frustum();
    const matrix = new THREE.Matrix4();
    
    if (this.camera instanceof THREE.PerspectiveCamera) {
      matrix.multiplyMatrices(this.camera.projectionMatrix, this.camera.matrixWorldInverse);
      frustum.setFromProjectionMatrix(matrix);
      
      this.scene.traverse((object) => {
        if (object instanceof THREE.Mesh && object.userData.originallyVisible !== false) {
          const inFrustum = frustum.intersectsObject(object);
          object.visible = inFrustum;
        }
      });
    }
  }

  /**
   * 优化几何体
   */
  public optimizeGeometry(geometry: THREE.BufferGeometry): THREE.BufferGeometry {
    const optimized = geometry.clone();
    
    // 合并顶点
    optimized.mergeVertices();
    
    // 计算法线（如果没有）
    if (!optimized.attributes.normal) {
      optimized.computeVertexNormals();
    }
    
    // 计算包围盒和包围球
    optimized.computeBoundingBox();
    optimized.computeBoundingSphere();
    
    return optimized;
  }

  /**
   * 优化纹理
   */
  public optimizeTexture(texture: THREE.Texture): THREE.Texture {
    // 设置合适的过滤器
    texture.minFilter = THREE.LinearMipmapLinearFilter;
    texture.magFilter = THREE.LinearFilter;
    
    // 生成mipmap
    texture.generateMipmaps = true;
    
    // 设置各向异性过滤
    if (this.renderer) {
      const maxAnisotropy = this.renderer.capabilities.getMaxAnisotropy();
      texture.anisotropy = Math.min(4, maxAnisotropy);
    }
    
    return texture;
  }

  /**
   * 获取性能建议
   */
  public getPerformanceRecommendations(): string[] {
    const recommendations: string[] = [];
    
    if (this.stats.fps < 30) {
      recommendations.push('FPS过低，建议启用更多优化选项');
    }
    
    if (this.stats.drawCalls > 1000) {
      recommendations.push('绘制调用过多，建议启用批处理或减少对象数量');
    }
    
    if (this.stats.triangles > 500000) {
      recommendations.push('三角形数量过多，建议使用LOD或简化模型');
    }
    
    if (this.stats.memoryUsage.total > 100) {
      recommendations.push('内存使用过高，建议优化纹理或几何体');
    }
    
    if (!this.settings.enableFrustumCulling) {
      recommendations.push('建议启用视锥剔除以提高性能');
    }
    
    return recommendations;
  }

  /**
   * 获取性能统计
   */
  public getStats(): ThreePerformanceStats {
    return { ...this.stats };
  }

  /**
   * 获取优化设置
   */
  public getSettings(): ThreeOptimizationSettings {
    return { ...this.settings };
  }

  /**
   * 更新优化设置
   */
  public updateSettings(newSettings: Partial<ThreeOptimizationSettings>): void {
    this.settings = { ...this.settings, ...newSettings };
    console.log('Three.js 优化设置已更新:', this.settings);
  }

  /**
   * 添加性能统计监听器
   */
  public addStatsUpdateListener(listener: (stats: ThreePerformanceStats) => void): void {
    this.statsUpdateListeners.push(listener);
  }

  /**
   * 移除性能统计监听器
   */
  public removeStatsUpdateListener(listener: (stats: ThreePerformanceStats) => void): void {
    const index = this.statsUpdateListeners.indexOf(listener);
    if (index > -1) {
      this.statsUpdateListeners.splice(index, 1);
    }
  }

  /**
   * 清理资源
   */
  public dispose(): void {
    this.statsUpdateListeners = [];
    this.renderer = null;
    this.scene = null;
    this.camera = null;
    
    console.log('Three.js 性能服务已清理');
  }
}

export default ThreePerformanceService;
