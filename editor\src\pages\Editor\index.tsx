/**
 * 编辑器主页面
 */
import React, { useState } from 'react';
import { Modal, message } from 'antd';
import {
  SaveOutlined,
  PlayCircleOutlined,
  SettingOutlined,
  ExportOutlined,
  ImportOutlined,
  UndoOutlined,
  RedoOutlined,
  PlusOutlined,
  DeleteOutlined,
  CopyOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  AppstoreOutlined,
  BookOutlined} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { EditorLayout } from '../../components/layout/EditorLayout';
import { ExampleBrowser } from '../../components/ExampleBrowser';
import './Editor.less';

const Editor: React.FC = () => {
  const { t } = useTranslation();
  const [exampleBrowserVisible, setExampleBrowserVisible] = useState<boolean>(false);

  // 从URL参数获取项目和场景ID
  const projectId = 'demo-project';
  const sceneId = 'main-scene';

  // 处理菜单点击
  const handleMenuClick = (e: { key: string }) => {
    switch (e.key) {
      case 'examples':
        setExampleBrowserVisible(true);
        break;
      case 'tutorials':
        // TODO: 打开教程浏览器
        message.info(t('editor.messages.tutorialsComingSoon'));
        break;
      default:
        message.info(`${t('editor.messages.clickedMenuItem')}: ${e.key}`);
        break;
    }
  };

  return (
    <div className="editor-container">
      {/* 使用新的编辑器布局组件 */}
      <EditorLayout projectId={projectId} sceneId={sceneId} />

      {/* 示例项目浏览器对话框 */}
      <Modal
        title={t('exampleBrowser.title')}
        open={exampleBrowserVisible}
        onCancel={() => setExampleBrowserVisible(false)}
        footer={null}
        width="90%"
        style={{ top: 20 }}
        styles={{ body: { padding: 0, height: 'calc(90vh - 55px)' } }}
      >
        <ExampleBrowser />
      </Modal>
    </div>
  );
};

export default Editor;
