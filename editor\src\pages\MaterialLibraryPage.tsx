/**
 * 材质库页面
 */
import React, { useState, useEffect } from 'react';
import { Layout, Card, Button, Modal, List, Tag, Dropdown, message, Spin, Empty, Input } from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  MoreOutlined,
  ExportOutlined,
  ImportOutlined} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { RootState, useAppDispatch } from '../store';
import MaterialEditor from '../components/MaterialEditor';
import { fetchMaterials, createMaterial, updateMaterial, deleteMaterial } from '../store/materials/materialsSlice';
import './MaterialLibraryPage.less';

const { Header, Content } = Layout;
const { Search } = Input;
const { Meta } = Card;

const MaterialLibraryPage: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { materials, loading } = useSelector((state: RootState) => state.materials);
  
  const [searchValue, setSearchValue] = useState<string>('');
  const [editorVisible, setEditorVisible] = useState<boolean>(false);
  const [currentMaterialId, setCurrentMaterialId] = useState<string | null>(null);
  
  // 加载材质
  useEffect(() => {
    dispatch(fetchMaterials());
  }, [dispatch]);
  
  // 过滤材质
  const filteredMaterials = materials.filter(material => 
    material.name.toLowerCase().includes(searchValue.toLowerCase())
  );
  
  // 打开编辑器
  const openEditor = (materialId?: string) => {
    setCurrentMaterialId(materialId || null);
    setEditorVisible(true);
  };
  
  // 关闭编辑器
  const closeEditor = () => {
    setEditorVisible(false);
    setCurrentMaterialId(null);
  };
  
  // 保存材质
  const handleSaveMaterial = (materialData: any) => {
    if (materialData.id) {
      dispatch(updateMaterial(materialData));
      message.success(t('editor.materialLibrary.updateSuccess'));
    } else {
      dispatch(createMaterial(materialData));
      message.success(t('editor.materialLibrary.createSuccess'));
    }
    closeEditor();
  };
  
  // 删除材质
  const handleDeleteMaterial = (materialId: string) => {
    Modal.confirm({
      title: t('editor.materialLibrary.confirmDelete'),
      content: t('editor.materialLibrary.confirmDeleteContent'),
      okText: t('common.delete'),
      okType: 'danger',
      cancelText: t('common.cancel'),
      onOk: () => {
        dispatch(deleteMaterial(materialId));
        message.success(t('editor.materialLibrary.deleteSuccess'));
      }});
  };
  
  // 复制材质
  const handleDuplicateMaterial = (material: any) => {
    const newMaterial = {
      ...material,
      id: undefined,
      name: `${material.name} (${t('common.copy')})`};
    dispatch(createMaterial(newMaterial));
    message.success(t('editor.materialLibrary.duplicateSuccess'));
  };
  
  // 渲染材质列表
  const renderMaterialList = () => {
    if (loading) {
      return (
        <div className="loading-container">
          <Spin size="large" />
        </div>
      );
    }
    
    if (filteredMaterials.length === 0) {
      return (
        <Empty
          description={
            searchValue
              ? t('editor.materialLibrary.noSearchResults')
              : t('editor.materialLibrary.noMaterials')
          }
        >
          <Button type="primary" icon={<PlusOutlined />} onClick={() => openEditor()}>
            {t('editor.materialLibrary.create')}
          </Button>
        </Empty>
      );
    }
    
    return (
      <List
        grid={{ gutter: 16, xs: 1, sm: 2, md: 3, lg: 4, xl: 4, xxl: 6 }}
        dataSource={filteredMaterials}
        renderItem={material => (
          <List.Item>
            <Card
              hoverable
              cover={
                <div
                  className="material-preview"
                  style={{ backgroundColor: material.color || '#ffffff' }}
                  onClick={() => openEditor(material.id)}
                />
              }
              actions={[
                <Button
                  type="text"
                  icon={<EditOutlined />}
                  onClick={() => openEditor(material.id)}
                />,
                <Dropdown
                  menu={{
                    items: [
                      {
                        key: 'duplicate',
                        icon: <CopyOutlined />,
                        label: t('editor.materialLibrary.duplicate'),
                        onClick: () => handleDuplicateMaterial(material)
                      },
                      {
                        key: 'export',
                        icon: <ExportOutlined />,
                        label: t('editor.materialLibrary.export'),
                        onClick: () => message.info(t('editor.materialLibrary.exportSuccess'))
                      },
                      {
                        key: 'delete',
                        icon: <DeleteOutlined />,
                        label: t('editor.materialLibrary.delete'),
                        danger: true,
                        onClick: () => handleDeleteMaterial(material.id)
                      }
                    ]
                  }}
                  trigger={['click']}
                >
                  <Button type="text" icon={<MoreOutlined />} />
                </Dropdown>,
              ]}
            >
              <Meta
                title={material.name}
                description={
                  <div className="material-type">
                    <Tag color="blue">{material.type}</Tag>
                  </div>
                }
              />
            </Card>
          </List.Item>
        )}
      />
    );
  };
  
  return (
    <Layout className="material-library-page">
      <Header className="page-header">
        <div className="header-title">
          <h1>{t('editor.materialLibrary.title')}</h1>
        </div>
        <div className="header-actions">
          <Search
            placeholder={t('editor.materialLibrary.search') as string}
            value={searchValue}
            onChange={e => setSearchValue(e.target.value)}
            style={{ width: 250 }}
            prefix={<SearchOutlined />}
          />
          <Button type="primary" icon={<PlusOutlined />} onClick={() => openEditor()}>
            {t('editor.materialLibrary.create')}
          </Button>
          <Button icon={<ImportOutlined />}>
            {t('editor.materialLibrary.import')}
          </Button>
        </div>
      </Header>
      
      <Content className="page-content">
        {renderMaterialList()}
      </Content>
      
      <Modal
        title={null}
        open={editorVisible}
        onCancel={closeEditor}
        footer={null}
        width="80%"
        style={{ top: 20 }}
        styles={{ body: { padding: 0, height: 'calc(90vh - 40px)' } }}
      >
        <MaterialEditor
          materialId={currentMaterialId || undefined}
          onSave={handleSaveMaterial}
          onCancel={closeEditor}
        />
      </Modal>
    </Layout>
  );
};

export default MaterialLibraryPage;
