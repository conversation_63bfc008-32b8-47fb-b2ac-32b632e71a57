/**
 * 增强资源面板组件
 * 集成AssetManagerService的完整资源管理功能
 */
import React, { useState, useEffect } from 'react';
import { 
  Card, 
  List, 
  Button, 
  Input, 
  Upload, 
  Modal, 
  message, 
  Tooltip,
  Space,
  Tag,
  Dropdown,
  Tabs,
  Empty,
  Image,
  Progress} from 'antd';
import {
  UploadOutlined,
  DeleteOutlined,
  EyeOutlined,
  SearchOutlined,
  AppstoreOutlined,
  BarsOutlined,
  FolderOutlined,
  FileOutlined,
  PlayCircleOutlined,
  PictureOutlined,
  VideoCameraOutlined,
  AudioOutlined,
  CodeOutlined,
  MoreOutlined} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';
import AssetManagerService, { Asset, AssetType, AssetCategory } from '../../services/AssetManagerService';

const { Search } = Input;
const { TabPane } = Tabs;
const { Dragger } = Upload;

const EnhancedAssetsPanel: React.FC = () => {
  const { t } = useTranslation();
  const { projectId } = useParams<{ projectId: string }>();
  
  const [assets, setAssets] = useState<Asset[]>([]);
  const [categories, setCategories] = useState<AssetCategory[]>([]);
  const [filteredAssets, setFilteredAssets] = useState<Asset[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedAssets, setSelectedAssets] = useState<string[]>([]);
  const [previewAsset, setPreviewAsset] = useState<Asset | null>(null);
  const [uploadModalVisible, setUploadModalVisible] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  // 初始化资源管理器
  useEffect(() => {
    const initializeAssets = async () => {
      if (!projectId) return;
      
      setIsLoading(true);
      try {
        const assetManager = AssetManagerService.getInstance();
        
        // 加载项目资源
        const projectAssets = await assetManager.loadProjectAssets(`/projects/${projectId}`);
        const assetCategories = assetManager.getCategories();
        
        setAssets(projectAssets);
        setCategories(assetCategories);
        setFilteredAssets(projectAssets);
        
        console.log('资源面板初始化完成，资源数量:', projectAssets.length);
      } catch (error) {
        console.error('初始化资源失败:', error);
        message.error('加载资源失败');
      } finally {
        setIsLoading(false);
      }
    };

    initializeAssets();
  }, [projectId]);

  // 过滤资源
  useEffect(() => {
    let filtered = assets;
    
    // 按分类过滤
    if (selectedCategory !== 'all') {
      const category = categories.find(cat => cat.id === selectedCategory);
      if (category) {
        filtered = filtered.filter(asset => category.types.includes(asset.type));
      }
    }
    
    // 按搜索查询过滤
    if (searchQuery) {
      filtered = filtered.filter(asset => 
        asset.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        asset.tags?.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }
    
    setFilteredAssets(filtered);
  }, [assets, selectedCategory, searchQuery, categories]);

  // 获取资源图标
  const getAssetIcon = (type: AssetType) => {
    switch (type) {
      case AssetType.MODEL:
        return <FolderOutlined />;
      case AssetType.TEXTURE:
        return <PictureOutlined />;
      case AssetType.AUDIO:
        return <AudioOutlined />;
      case AssetType.VIDEO:
        return <VideoCameraOutlined />;
      case AssetType.SCRIPT:
        return <CodeOutlined />;
      default:
        return <FileOutlined />;
    }
  };

  // 获取文件大小显示
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 处理文件上传
  const handleUpload = async (file: File) => {
    try {
      setUploadProgress(0);
      const assetManager = AssetManagerService.getInstance();
      
      // 模拟上传进度
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 100);

      const newAsset = await assetManager.importAsset(file, {
        generateThumbnail: true,
        optimizeForWeb: true,
        compressionLevel: 0.8,
        generateMipmaps: true
      });

      clearInterval(progressInterval);
      setUploadProgress(100);

      if (newAsset) {
        setAssets(prev => [...prev, newAsset]);
        message.success(`资源 "${newAsset.name}" 导入成功`);
      } else {
        message.error('资源导入失败');
      }

      setTimeout(() => {
        setUploadProgress(0);
        setUploadModalVisible(false);
      }, 1000);

      return false; // 阻止默认上传行为
    } catch (error) {
      console.error('上传失败:', error);
      message.error('上传失败');
      setUploadProgress(0);
      return false;
    }
  };

  // 删除资源
  const handleDeleteAsset = (assetId: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个资源吗？此操作不可撤销。',
      onOk: () => {
        const assetManager = AssetManagerService.getInstance();
        if (assetManager.deleteAsset(assetId)) {
          setAssets(prev => prev.filter(asset => asset.id !== assetId));
          setSelectedAssets(prev => prev.filter(id => id !== assetId));
          message.success('资源删除成功');
        } else {
          message.error('资源删除失败');
        }
      }
    });
  };

  // 预览资源
  const handlePreviewAsset = async (asset: Asset) => {
    try {
      const assetManager = AssetManagerService.getInstance();
      const data = await assetManager.loadAssetData(asset);
      
      if (data) {
        setPreviewAsset(asset);
        console.log('预览资源:', asset.name, data);
      } else {
        message.error('无法预览此资源');
      }
    } catch (error) {
      console.error('预览失败:', error);
      message.error('预览失败');
    }
  };

  // 渲染网格视图
  const renderGridView = () => (
    <List
      grid={{ gutter: 16, xs: 2, sm: 3, md: 4, lg: 5, xl: 6 }}
      dataSource={filteredAssets}
      loading={isLoading}
      renderItem={(asset) => (
        <List.Item>
          <Card
            hoverable
            size="small"
            cover={
              asset.thumbnail ? (
                <Image
                  alt={asset.name}
                  src={asset.thumbnail}
                  style={{ height: 80, objectFit: 'cover' }}
                  preview={false}
                />
              ) : (
                <div style={{ 
                  height: 80, 
                  display: 'flex', 
                  alignItems: 'center', 
                  justifyContent: 'center',
                  background: '#f5f5f5'
                }}>
                  {getAssetIcon(asset.type)}
                </div>
              )
            }
            actions={[
              <Tooltip title="预览">
                <Button 
                  type="text" 
                  icon={<EyeOutlined />} 
                  onClick={() => handlePreviewAsset(asset)}
                />
              </Tooltip>,
              <Tooltip title="删除">
                <Button 
                  type="text" 
                  icon={<DeleteOutlined />} 
                  danger
                  onClick={() => handleDeleteAsset(asset.id)}
                />
              </Tooltip>
            ]}
          >
            <Card.Meta
              title={
                <Tooltip title={asset.name}>
                  <div style={{ 
                    overflow: 'hidden', 
                    textOverflow: 'ellipsis', 
                    whiteSpace: 'nowrap' 
                  }}>
                    {asset.name}
                  </div>
                </Tooltip>
              }
              description={
                <div>
                  <Tag size="small">{asset.type}</Tag>
                  {asset.size && (
                    <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                      {formatFileSize(asset.size)}
                    </div>
                  )}
                </div>
              }
            />
          </Card>
        </List.Item>
      )}
    />
  );

  // 渲染列表视图
  const renderListView = () => (
    <List
      dataSource={filteredAssets}
      loading={isLoading}
      renderItem={(asset) => (
        <List.Item
          actions={[
            <Button type="text" icon={<EyeOutlined />} onClick={() => handlePreviewAsset(asset)} />,
            <Button type="text" icon={<DeleteOutlined />} danger onClick={() => handleDeleteAsset(asset.id)} />
          ]}
        >
          <List.Item.Meta
            avatar={getAssetIcon(asset.type)}
            title={asset.name}
            description={
              <Space>
                <Tag>{asset.type}</Tag>
                {asset.size && <span>{formatFileSize(asset.size)}</span>}
                {asset.format && <span>{asset.format.toUpperCase()}</span>}
              </Space>
            }
          />
        </List.Item>
      )}
    />
  );

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* 工具栏 */}
      <div style={{ padding: '8px', borderBottom: '1px solid #f0f0f0' }}>
        <Space style={{ width: '100%', justifyContent: 'space-between' }}>
          <Search
            placeholder="搜索资源..."
            allowClear
            style={{ width: 200 }}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <Space>
            <Button
              type="primary"
              icon={<UploadOutlined />}
              onClick={() => setUploadModalVisible(true)}
            >
              导入
            </Button>
            <Button
              type={viewMode === 'grid' ? 'primary' : 'default'}
              icon={<AppstoreOutlined />}
              onClick={() => setViewMode('grid')}
            />
            <Button
              type={viewMode === 'list' ? 'primary' : 'default'}
              icon={<BarsOutlined />}
              onClick={() => setViewMode('list')}
            />
          </Space>
        </Space>
      </div>

      {/* 分类标签 */}
      <Tabs
        activeKey={selectedCategory}
        onChange={setSelectedCategory}
        size="small"
        style={{ padding: '0 8px' }}
      >
        <TabPane tab={`全部 (${assets.length})`} key="all" />
        {categories.map(category => (
          <TabPane 
            tab={`${category.name} (${category.count})`} 
            key={category.id} 
          />
        ))}
      </Tabs>

      {/* 资源列表 */}
      <div style={{ flex: 1, padding: '8px', overflow: 'auto' }}>
        {filteredAssets.length === 0 ? (
          <Empty description="暂无资源" />
        ) : (
          viewMode === 'grid' ? renderGridView() : renderListView()
        )}
      </div>

      {/* 上传模态框 */}
      <Modal
        title="导入资源"
        open={uploadModalVisible}
        onCancel={() => setUploadModalVisible(false)}
        footer={null}
      >
        <Dragger
          multiple
          beforeUpload={handleUpload}
          showUploadList={false}
        >
          <p className="ant-upload-drag-icon">
            <UploadOutlined />
          </p>
          <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p className="ant-upload-hint">
            支持模型、纹理、音频、视频等多种格式
          </p>
        </Dragger>
        
        {uploadProgress > 0 && (
          <Progress 
            percent={uploadProgress} 
            style={{ marginTop: 16 }}
          />
        )}
      </Modal>

      {/* 预览模态框 */}
      <Modal
        title={previewAsset?.name}
        open={!!previewAsset}
        onCancel={() => setPreviewAsset(null)}
        footer={null}
        width={600}
      >
        {previewAsset && (
          <div style={{ textAlign: 'center' }}>
            {previewAsset.type === AssetType.TEXTURE && previewAsset.thumbnail && (
              <Image src={previewAsset.thumbnail} alt={previewAsset.name} />
            )}
            {previewAsset.type === AssetType.AUDIO && (
              <audio controls style={{ width: '100%' }}>
                <source src={previewAsset.path} />
              </audio>
            )}
            {previewAsset.type === AssetType.VIDEO && (
              <video controls style={{ width: '100%', maxHeight: '400px' }}>
                <source src={previewAsset.path} />
              </video>
            )}
            <div style={{ marginTop: 16, textAlign: 'left' }}>
              <p><strong>类型:</strong> {previewAsset.type}</p>
              <p><strong>格式:</strong> {previewAsset.format}</p>
              {previewAsset.size && (
                <p><strong>大小:</strong> {formatFileSize(previewAsset.size)}</p>
              )}
              {previewAsset.tags && previewAsset.tags.length > 0 && (
                <p><strong>标签:</strong> {previewAsset.tags.map(tag => (
                  <Tag key={tag} style={{ marginLeft: 4 }}>{tag}</Tag>
                ))}</p>
              )}
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default EnhancedAssetsPanel;
