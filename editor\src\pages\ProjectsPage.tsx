/**
 * 项目页面
 */
import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Button,
  List,
  Typography,
  Space,
  Input,
  Modal,
  Form,
  Select,
  Tabs,
  Tag,
  Tooltip,
  Empty,
  Spin,
  message} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  FolderOpenOutlined,
  ClockCircleOutlined,
  LockOutlined,
  UnlockOutlined,
  AppstoreOutlined,
  BarsOutlined} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useAppDispatch, useAppSelector } from '../store';
import {
  fetchProjects,
  createProject,
  deleteProject,
  createScene,
  setCurrentProject,
  setCurrentScene} from '../store/project/projectSlice';
import { exampleProjects } from '../data/exampleProjects';
import ProjectService from '../services/ProjectService';

const { Title } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { Search } = Input;

export const ProjectsPage: React.FC = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();


  
  const { isAuthenticated, isLoading: authLoading } = useAppSelector((state) => state.auth);
  const { projects, isLoading, error } = useAppSelector((state) => state.project);
  
  const [searchValue, setSearchValue] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [newProjectModalVisible, setNewProjectModalVisible] = useState(false);
  const [newSceneModalVisible, setNewSceneModalVisible] = useState(false);
  const [deleteProjectModalVisible, setDeleteProjectModalVisible] = useState(false);
  const [selectedProject, setSelectedProject] = useState<any>(null);
  
  const [form] = Form.useForm();
  const [sceneForm] = Form.useForm();
  const [activeTab, setActiveTab] = useState('my-projects');
  const [exampleProjectsData, setExampleProjectsData] = useState<any[]>([]);
  
  // 检查认证状态（避免刚刷新时本地有 token 但尚未完成 checkAuth 就被重定向）
  useEffect(() => {
    const token = (typeof window !== 'undefined' && window.localStorage) ? localStorage.getItem('token') : null;
    if (!isAuthenticated && !authLoading && !token) {
      console.log('项目页面：用户未认证，重定向到登录页面');
      navigate('/login', { state: { from: '/projects' }, replace: true });
    }
  }, [isAuthenticated, authLoading, navigate]);

  // 加载项目列表
  useEffect(() => {
    if (isAuthenticated) {
      dispatch(fetchProjects());
    }
  }, [dispatch, isAuthenticated]);

  // 加载示例项目
  useEffect(() => {
    const loadExampleProjects = async () => {
      try {
        const projectService = ProjectService.getInstance();
        const defaultProject = await projectService.loadDefaultProject();

        // 将示例项目转换为显示格式
        const exampleData = exampleProjects.map(example => ({
          id: example.id,
          name: example.title,
          description: example.description,
          thumbnail: example.thumbnailUrl,
          category: example.category,
          difficulty: example.difficulty,
          tags: example.tags,
          estimatedTime: example.estimatedTime,
          path: example.path,
          isExample: true
        }));

        setExampleProjectsData(exampleData);
      } catch (error) {
        console.error('加载示例项目失败:', error);
      }
    };

    loadExampleProjects();
  }, []);

  // 处理错误 - 更加宽松的错误处理，避免因API错误导致重定向
  useEffect(() => {
    if (error && isAuthenticated) {
      // 只显示真正的业务错误，忽略API不存在等技术错误
      if (!error.includes('Unauthorized') &&
          !error.includes('401') &&
          !error.includes('404') &&
          !error.includes('Cannot GET') &&
          !error.includes('获取项目列表失败')) {
        message.error(error);
      } else {
        // 对于API相关错误，只在控制台记录
        console.warn('项目API错误（已忽略）:', error);
      }
    }
  }, [error, isAuthenticated]);
  
  // 过滤项目
  const filteredProjects = projects.filter(
    (project) =>
      project.name.toLowerCase().includes(searchValue.toLowerCase()) ||
      project.description.toLowerCase().includes(searchValue.toLowerCase())
  );

  // 获取我的项目（当前用户创建的项目）
  const myProjects = projects.filter(
    (project) => project.ownerId === 'current-user' // TODO: 替换为实际的用户ID
  ).filter(
    (project) =>
      project.name.toLowerCase().includes(searchValue.toLowerCase()) ||
      project.description.toLowerCase().includes(searchValue.toLowerCase())
  );

  // 获取共享项目（其他用户创建但当前用户有权限访问的项目）
  const sharedProjects = projects.filter(
    (project) => project.ownerId !== 'current-user' && project.isPublic
  ).filter(
    (project) =>
      project.name.toLowerCase().includes(searchValue.toLowerCase()) ||
      project.description.toLowerCase().includes(searchValue.toLowerCase())
  );
  
  // 创建新项目
  const handleCreateProject = () => {
    form.validateFields().then((values) => {
      dispatch(createProject(values))
        .unwrap()
        .then(() => {
          setNewProjectModalVisible(false);
          form.resetFields();
          message.success(t('projects.createSuccess'));
        })
        .catch((error) => {
          message.error(error || t('projects.createError'));
        });
    });
  };
  
  // 创建新场景
  const handleCreateScene = () => {
    if (!selectedProject) return;
    
    sceneForm.validateFields().then((values) => {
      dispatch(createScene({ projectId: selectedProject.id, ...values }))
        .unwrap()
        .then((scene) => {
          setNewSceneModalVisible(false);
          sceneForm.resetFields();
          message.success(t('projects.sceneCreateSuccess'));
          
          // 导航到编辑器
          dispatch(setCurrentProject(selectedProject));
          dispatch(setCurrentScene(scene));
          navigate(`/editor/${selectedProject.id}/${scene.id}`);
        })
        .catch((error) => {
          message.error(error || t('projects.sceneCreateError'));
        });
    });
  };
  
  // 删除项目
  const handleDeleteProject = () => {
    if (!selectedProject) return;
    
    dispatch(deleteProject(selectedProject.id))
      .unwrap()
      .then(() => {
        setDeleteProjectModalVisible(false);
        setSelectedProject(null);
        message.success(t('projects.deleteSuccess'));
      })
      .catch((error) => {
        message.error(error || t('projects.deleteError'));
      });
  };
  
  // 打开项目
  const handleOpenProject = (project: any) => {
    console.log('打开项目:', project);
    dispatch(setCurrentProject(project));

    // 如果项目有场景，打开第一个场景
    if (project.scenes && project.scenes.length > 0) {
      const scene = project.scenes[0];
      console.log('打开场景:', scene);
      dispatch(setCurrentScene(scene));
      const editorPath = `/editor/${project.id}/${scene.id}`;
      console.log('导航到编辑器:', editorPath);
      navigate(editorPath);
    } else {
      // 否则打开新建场景对话框
      console.log('项目没有场景，打开新建场景对话框');
      setSelectedProject(project);
      setNewSceneModalVisible(true);
    }
  };

  // 打开示例项目
  const handleOpenExampleProject = (example: any) => {
    console.log('打开示例项目:', example);

    if (example.id.includes('scene')) {
      // 如果是场景示例，直接打开默认项目的对应场景
      const sceneId = example.id.replace('-scene', '');
      navigate(`/editor/default-project/${sceneId}`);
    } else {
      // 如果是其他示例，使用原有逻辑
      navigate(`/editor/example/${example.id}`);
    }
  };

  // 渲染网格视图
  const renderGridView = (projectList: Project[] = filteredProjects) => {
    return (
      <List
        grid={{ gutter: 16, xs: 1, sm: 2, md: 3, lg: 3, xl: 4, xxl: 6 }}
        dataSource={projectList}
        renderItem={(project) => (
          <List.Item>
            <Card
              hoverable
              cover={
                project.thumbnail ? (
                  <img alt={project.name} src={project.thumbnail} style={{ height: 160, objectFit: 'cover' }} />
                ) : (
                  <div
                    style={{
                      height: 160,
                      background: '#f0f0f0',
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center'}}
                  >
                    <FolderOpenOutlined style={{ fontSize: 48, color: '#999' }} />
                  </div>
                )
              }
              actions={[
                <Tooltip title={t('projects.open')}>
                  <Button type="text" icon={<FolderOpenOutlined />} onClick={() => handleOpenProject(project)} />
                </Tooltip>,
                <Tooltip title={t('projects.edit')}>
                  <Button type="text" icon={<EditOutlined />} onClick={() => handleOpenProject(project)} />
                </Tooltip>,
                <Tooltip title={t('projects.delete')}>
                  <Button
                    type="text"
                    icon={<DeleteOutlined />}
                    onClick={() => {
                      setSelectedProject(project);
                      setDeleteProjectModalVisible(true);
                    }}
                  />
                </Tooltip>,
              ]}
            >
              <Card.Meta
                title={project.name}
                description={
                  <>
                    <div style={{ marginBottom: 8 }}>{project.description}</div>
                    <div>
                      <Space>
                        <Tag icon={<ClockCircleOutlined />}>
                          {new Date(project.updatedAt).toLocaleDateString()}
                        </Tag>
                        <Tag icon={project.isPublic ? <UnlockOutlined /> : <LockOutlined />}>
                          {project.isPublic ? t('projects.public') : t('projects.private')}
                        </Tag>
                      </Space>
                    </div>
                  </>
                }
              />
            </Card>
          </List.Item>
        )}
      />
    );
  };
  
  // 渲染列表视图
  const renderListView = (projectList: Project[] = filteredProjects) => {
    return (
      <List
        itemLayout="horizontal"
        dataSource={projectList}
        renderItem={(project) => (
          <List.Item
            actions={[
              <Button type="link" onClick={() => handleOpenProject(project)}>
                {t('projects.open')}
              </Button>,
              <Button type="text" icon={<EditOutlined />} onClick={() => handleOpenProject(project)} />,
              <Button
                type="text"
                icon={<DeleteOutlined />}
                onClick={() => {
                  setSelectedProject(project);
                  setDeleteProjectModalVisible(true);
                }}
              />,
            ]}
          >
            <List.Item.Meta
              avatar={
                project.thumbnail ? (
                  <img
                    src={project.thumbnail}
                    alt={project.name}
                    style={{ width: 48, height: 48, objectFit: 'cover', borderRadius: 4 }}
                  />
                ) : (
                  <div
                    style={{
                      width: 48,
                      height: 48,
                      background: '#f0f0f0',
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                      borderRadius: 4}}
                  >
                    <FolderOpenOutlined style={{ fontSize: 24, color: '#999' }} />
                  </div>
                )
              }
              title={project.name}
              description={
                <>
                  <div>{project.description}</div>
                  <div style={{ marginTop: 4 }}>
                    <Space>
                      <Tag icon={<ClockCircleOutlined />}>
                        {new Date(project.updatedAt).toLocaleDateString()}
                      </Tag>
                      <Tag icon={project.isPublic ? <UnlockOutlined /> : <LockOutlined />}>
                        {project.isPublic ? t('projects.public') : t('projects.private')}
                      </Tag>
                    </Space>
                  </div>
                </>
              }
            />
          </List.Item>
        )}
      />
    );
  };

  // 渲染示例项目网格视图
  const renderExampleProjectsGrid = () => {
    return (
      <List
        grid={{ gutter: 16, xs: 1, sm: 2, md: 3, lg: 3, xl: 4, xxl: 6 }}
        dataSource={exampleProjectsData}
        renderItem={(example) => (
          <List.Item>
            <Card
              hoverable
              cover={
                example.thumbnail ? (
                  <img alt={example.name} src={example.thumbnail} style={{ height: 160, objectFit: 'cover' }} />
                ) : (
                  <div
                    style={{
                      height: 160,
                      background: '#f0f0f0',
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center'}}
                  >
                    <FolderOpenOutlined style={{ fontSize: 48, color: '#999' }} />
                  </div>
                )
              }
              actions={[
                <Tooltip title="打开示例">
                  <Button type="text" icon={<FolderOpenOutlined />} onClick={() => handleOpenExampleProject(example)} />
                </Tooltip>
              ]}
            >
              <Card.Meta
                title={example.name}
                description={
                  <>
                    <div style={{ marginBottom: 8 }}>{example.description}</div>
                    <div>
                      <Space>
                        <Tag color="blue">{example.category}</Tag>
                        <Tag color="green">{example.difficulty}</Tag>
                        {example.estimatedTime && (
                          <Tag icon={<ClockCircleOutlined />}>
                            {example.estimatedTime}分钟
                          </Tag>
                        )}
                      </Space>
                    </div>
                  </>
                }
              />
            </Card>
          </List.Item>
        )}
      />
    );
  };

  return (
    <div style={{ padding: 24 }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 24 }}>
        <Title level={2}>{t('projects.title')}</Title>
        <Space>
          <Search
            placeholder={t('projects.search') as string}
            allowClear
            onChange={(e) => setSearchValue(e.target.value)}
            style={{ width: 250 }}
          />
          <Button type="primary" icon={<PlusOutlined />} onClick={() => setNewProjectModalVisible(true)}>
            {t('projects.new')}
          </Button>
          <Tooltip title={t('projects.gridView')}>
            <Button
              type={viewMode === 'grid' ? 'primary' : 'default'}
              icon={<AppstoreOutlined />}
              onClick={() => setViewMode('grid')}
            />
          </Tooltip>
          <Tooltip title={t('projects.listView')}>
            <Button
              type={viewMode === 'list' ? 'primary' : 'default'}
              icon={<BarsOutlined />}
              onClick={() => setViewMode('list')}
            />
          </Tooltip>
        </Space>
      </div>
      
      <Tabs defaultActiveKey="all">
        <TabPane tab={t('projects.allProjects')} key="all">
          {isLoading ? (
            <div style={{ display: 'flex', justifyContent: 'center', padding: 40 }}>
              <Spin size="large" />
            </div>
          ) : filteredProjects.length > 0 ? (
            viewMode === 'grid' ? renderGridView(filteredProjects) : renderListView(filteredProjects)
          ) : (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={
                searchValue ? t('projects.noSearchResults') : t('projects.noProjects')
              }
            >
              <Button type="primary" icon={<PlusOutlined />} onClick={() => setNewProjectModalVisible(true)}>
                {t('projects.createFirst')}
              </Button>
            </Empty>
          )}
        </TabPane>
        <TabPane tab={t('projects.myProjects')} key="my">
          {isLoading ? (
            <div style={{ display: 'flex', justifyContent: 'center', padding: 40 }}>
              <Spin size="large" />
            </div>
          ) : myProjects.length > 0 ? (
            viewMode === 'grid' ? renderGridView(myProjects) : renderListView(myProjects)
          ) : (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={
                searchValue ? t('projects.noSearchResults') : t('projects.noMyProjects')
              }
            >
              <Button type="primary" icon={<PlusOutlined />} onClick={() => setNewProjectModalVisible(true)}>
                {t('projects.createFirst')}
              </Button>
            </Empty>
          )}
        </TabPane>
        <TabPane tab={t('projects.shared')} key="shared">
          {isLoading ? (
            <div style={{ display: 'flex', justifyContent: 'center', padding: 40 }}>
              <Spin size="large" />
            </div>
          ) : sharedProjects.length > 0 ? (
            viewMode === 'grid' ? renderGridView(sharedProjects) : renderListView(sharedProjects)
          ) : (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={
                searchValue ? t('projects.noSearchResults') : t('projects.noSharedProjects')
              }
            />
          )}
        </TabPane>

        <TabPane tab="示例项目" key="examples">
          {exampleProjectsData.length === 0 ? (
            <Empty description="暂无示例项目" />
          ) : (
            renderExampleProjectsGrid()
          )}
        </TabPane>
      </Tabs>
      
      {/* 新建项目对话框 */}
      <Modal
        title={t('projects.newProject')}
        open={newProjectModalVisible}
        onOk={handleCreateProject}
        onCancel={() => setNewProjectModalVisible(false)}
        okText={t('common.create')}
        cancelText={t('common.cancel')}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="name"
            label={t('projects.name')}
            rules={[{ required: true, message: t('projects.nameRequired') as string }]}
          >
            <Input placeholder={t('projects.namePlaceholder') as string} />
          </Form.Item>
          <Form.Item name="description" label={t('projects.description')}>
            <Input.TextArea rows={4} placeholder={t('projects.descriptionPlaceholder') as string} />
          </Form.Item>
          <Form.Item name="isPublic" label={t('projects.visibility')} initialValue={false}>
            <Select>
              <Option value={false}>{t('projects.private')}</Option>
              <Option value={true}>{t('projects.public')}</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
      
      {/* 新建场景对话框 */}
      <Modal
        title={t('projects.newScene')}
        open={newSceneModalVisible}
        onOk={handleCreateScene}
        onCancel={() => setNewSceneModalVisible(false)}
        okText={t('common.create')}
        cancelText={t('common.cancel')}
      >
        <Form form={sceneForm} layout="vertical">
          <Form.Item
            name="name"
            label={t('projects.sceneName')}
            rules={[{ required: true, message: t('projects.sceneNameRequired') as string }]}
          >
            <Input placeholder={t('projects.sceneNamePlaceholder') as string} />
          </Form.Item>
          <Form.Item name="description" label={t('projects.sceneDescription')}>
            <Input.TextArea rows={4} placeholder={t('projects.sceneDescriptionPlaceholder') as string} />
          </Form.Item>
        </Form>
      </Modal>
      
      {/* 删除项目确认对话框 */}
      <Modal
        title={t('projects.deleteConfirmTitle')}
        open={deleteProjectModalVisible}
        onOk={handleDeleteProject}
        onCancel={() => setDeleteProjectModalVisible(false)}
        okText={t('common.delete')}
        cancelText={t('common.cancel')}
        okButtonProps={{ danger: true }}
      >
        <p>
          {t('projects.deleteConfirmMessage', {
            name: selectedProject?.name})}
        </p>
        <p>{t('projects.deleteConfirmWarning')}</p>
      </Modal>
    </div>
  );
};
