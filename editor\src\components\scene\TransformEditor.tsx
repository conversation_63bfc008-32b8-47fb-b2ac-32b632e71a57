/**
 * 变换组件编辑器
 */
import React, { useEffect, useState, useCallback, useMemo, useRef } from 'react';
import { Card, Form, InputNumber, Space, Typography, Button, Tooltip, Row, Col, Divider, Radio } from 'antd';
import { ReloadOutlined, CopyOutlined, SnippetsOutlined, LinkOutlined, GlobalOutlined, HomeOutlined } from '@ant-design/icons';
import { useSelector, useDispatch } from 'react-redux';
import { debounce } from 'lodash';
import EngineService from '../../services/EngineService';
import { addToUndoStack } from '../../store/editor/editorSlice';
import type { Entity, Transform } from '../../libs/dl-engine';

const { Text } = Typography;

/**
 * 3D向量接口
 */
interface Vector3 {
  x: number;
  y: number;
  z: number;
}

/**
 * 变换数据接口
 */
interface TransformData {
  position: Vector3;
  rotation: Vector3;
  scale: Vector3;
}

/**
 * 变换编辑器属性
 */
interface TransformEditorProps {
  /** 实体ID（可选，如果不提供则使用当前选中的实体） */
  entityId?: string;
  /** 组件数据（可选，用于外部控制） */
  data?: TransformData;
  /** 数据更新回调 */
  onChange?: (data: TransformData) => void;
  /** 是否只读 */
  readonly?: boolean;
}

/**
 * 变换编辑器组件
 */
const TransformEditor: React.FC<TransformEditorProps> = ({
  entityId,
  data,
  onChange,
  readonly = false
}) => {
  const dispatch = useDispatch();
  const [form] = Form.useForm();
  const [isLinked, setIsLinked] = useState(false);
  const [clipboard, setClipboard] = useState<TransformData | null>(null);
  const [currentEntity, setCurrentEntity] = useState<Entity | null>(null);
  const [currentTransform, setCurrentTransform] = useState<Transform | null>(null);
  const [coordinateSpace, setCoordinateSpace] = useState<'local' | 'world'>('local');

  // 撤销/重做相关状态
  const previousTransformData = useRef<TransformData | null>(null);
  const isUndoRedoOperation = useRef<boolean>(false);

  // 从Redux获取当前选中的实体
  const selectedObject = useSelector((state: any) => state.editor.selectedObject);

  // 获取当前要编辑的实体
  const targetEntity = useMemo(() => {
    if (entityId) {
      // 如果指定了实体ID，通过引擎服务查找实体
      const scene = EngineService.getActiveScene();
      if (scene) {
        const entities = scene.getEntities();
        return entities.find((e: any) => e.id === entityId) || null;
      }
    }
    return selectedObject;
  }, [entityId, selectedObject]);

  // 默认变换数据
  const defaultData: TransformData = useMemo(() => ({
    position: { x: 0, y: 0, z: 0 },
    rotation: { x: 0, y: 0, z: 0 },
    scale: { x: 1, y: 1, z: 1 },
    ...data
  }), [data]);

  // 获取当前变换数据
  const getCurrentTransformData = useCallback((): TransformData => {
    if (currentTransform) {
      // 根据坐标空间获取不同的数据
      const position = coordinateSpace === 'world'
        ? currentTransform.getWorldPosition()
        : currentTransform.getPosition();
      // 注意：目前引擎只支持世界位置，旋转和缩放暂时只使用本地坐标
      const rotation = currentTransform.getRotation();
      const scale = currentTransform.getScale();

      return {
        position: { x: position.x, y: position.y, z: position.z },
        rotation: {
          x: rotation.x * (180 / Math.PI), // 转换为度数
          y: rotation.y * (180 / Math.PI),
          z: rotation.z * (180 / Math.PI)
        },
        scale: { x: scale.x, y: scale.y, z: scale.z }
      };
    }
    return data || defaultData;
  }, [currentTransform, data, defaultData, coordinateSpace]);

  // 保存变换状态到撤销栈
  const saveTransformToUndoStack = useCallback((oldData: TransformData, newData: TransformData) => {
    if (isUndoRedoOperation.current) return; // 避免撤销/重做操作时重复保存

    const undoOperation = {
      type: 'TRANSFORM_CHANGE',
      entityId: currentEntity?.id,
      oldData,
      newData,
      timestamp: Date.now()
    };

    dispatch(addToUndoStack(undoOperation));
  }, [currentEntity, dispatch]);

  // 防抖的变换更新函数
  const debouncedUpdateTransform = useCallback(
    debounce((transformData: TransformData) => {
      if (currentTransform && !readonly) {
        // 保存当前状态到撤销栈
        if (previousTransformData.current) {
          saveTransformToUndoStack(previousTransformData.current, transformData);
        }

        // 更新引擎中的变换
        currentTransform.setPosition(
          transformData.position.x,
          transformData.position.y,
          transformData.position.z
        );
        currentTransform.setRotation(
          transformData.rotation.x * (Math.PI / 180), // 转换为弧度
          transformData.rotation.y * (Math.PI / 180),
          transformData.rotation.z * (Math.PI / 180)
        );
        currentTransform.setScale(
          transformData.scale.x,
          transformData.scale.y,
          transformData.scale.z
        );

        // 更新前一个状态
        previousTransformData.current = transformData;
      }

      // 调用外部回调
      if (onChange) {
        onChange(transformData);
      }
    }, 100),
    [currentTransform, onChange, readonly, saveTransformToUndoStack]
  );

  // 初始化和更新当前实体和变换组件
  useEffect(() => {
    if (targetEntity) {
      setCurrentEntity(targetEntity);
      const transform = targetEntity.getTransform();
      setCurrentTransform(transform);

      // 更新表单数据
      const transformData = getCurrentTransformData();
      form.setFieldsValue(transformData);

      // 保存初始状态
      previousTransformData.current = transformData;

      // 监听变换变化事件
      const handleTransformChange = () => {
        isUndoRedoOperation.current = true; // 标记为引擎触发的变化
        const newTransformData = getCurrentTransformData();
        form.setFieldsValue(newTransformData);
        previousTransformData.current = newTransformData;
        isUndoRedoOperation.current = false;
      };

      if (transform) {
        transform.on('positionChanged', handleTransformChange);
        transform.on('rotationChanged', handleTransformChange);
        transform.on('scaleChanged', handleTransformChange);
      }

      // 清理函数
      return () => {
        if (transform) {
          transform.off('positionChanged', handleTransformChange);
          transform.off('rotationChanged', handleTransformChange);
          transform.off('scaleChanged', handleTransformChange);
        }
      };
    } else {
      setCurrentEntity(null);
      setCurrentTransform(null);
      previousTransformData.current = null;
      form.resetFields();
    }
  }, [targetEntity, form, getCurrentTransformData]);

  // 处理表单值变化
  const handleValuesChange = useCallback((_changedValues: any, allValues: TransformData) => {
    if (readonly) return;
    debouncedUpdateTransform(allValues);
  }, [debouncedUpdateTransform, readonly]);

  // 重置变换
  const handleReset = useCallback(() => {
    if (readonly) return;
    const resetData: TransformData = {
      position: { x: 0, y: 0, z: 0 },
      rotation: { x: 0, y: 0, z: 0 },
      scale: { x: 1, y: 1, z: 1 }
    };
    form.setFieldsValue(resetData);
    debouncedUpdateTransform(resetData);
  }, [form, debouncedUpdateTransform, readonly]);

  // 复制变换数据
  const handleCopy = useCallback(() => {
    const currentData = getCurrentTransformData();
    setClipboard(currentData);
  }, [getCurrentTransformData]);

  // 粘贴变换数据
  const handlePaste = useCallback(() => {
    if (readonly || !clipboard) return;
    form.setFieldsValue(clipboard);
    debouncedUpdateTransform(clipboard);
  }, [clipboard, form, debouncedUpdateTransform, readonly]);

  // 切换缩放链接状态
  const handleToggleLink = useCallback(() => {
    setIsLinked(!isLinked);
  }, [isLinked]);

  // 处理链接缩放
  const handleScaleChange = useCallback((_field: 'x' | 'y' | 'z', value: number) => {
    if (isLinked) {
      const currentValues = form.getFieldsValue();
      const newScale = { x: value, y: value, z: value };
      const newValues = { ...currentValues, scale: newScale };
      form.setFieldsValue(newValues);
      debouncedUpdateTransform(newValues);
    }
  }, [isLinked, form, debouncedUpdateTransform]);

  // 如果没有选中实体，显示提示
  if (!currentEntity) {
    return (
      <Card title={t('editor.transform.title')} size="small">
        <div style={{ textAlign: 'center', padding: '20px', color: '#999' }}>
          <Text type="secondary">{t('editor.transform.selectEntity')}</Text>
        </div>
      </Card>
    );
  }

  return (
    <Card
      title={t('editor.transform.title')}
      size="small"
      extra={
        !readonly && (
          <Space size="small">
            <Tooltip title={t('editor.transform.copy')}>
              <Button
                type="text"
                size="small"
                icon={<CopyOutlined />}
                onClick={handleCopy}
              />
            </Tooltip>
            <Tooltip title={t('editor.transform.paste')}>
              <Button
                type="text"
                size="small"
                icon={<SnippetsOutlined />}
                onClick={handlePaste}
                disabled={!clipboard}
              />
            </Tooltip>
            <Tooltip title={t('editor.transform.reset')}>
              <Button
                type="text"
                size="small"
                icon={<ReloadOutlined />}
                onClick={handleReset}
              />
            </Tooltip>
          </Space>
        )
      }
    >
      {/* 坐标空间切换 */}
      <div style={{ marginBottom: '12px' }}>
        <Radio.Group
          value={coordinateSpace}
          onChange={(e) => setCoordinateSpace(e.target.value)}
          size="small"
        >
          <Radio.Button value="local">
            <HomeOutlined style={{ marginRight: '4px' }} />
            本地
          </Radio.Button>
          <Radio.Button value="world">
            <GlobalOutlined style={{ marginRight: '4px' }} />
            世界
          </Radio.Button>
        </Radio.Group>
      </div>
      <Form
        form={form}
        layout="vertical"
        size="small"
        onValuesChange={handleValuesChange}
        disabled={readonly}
      >
        {/* 位置 */}
        <Form.Item label={t('editor.transform.position')} style={{ marginBottom: 12 }}>
          <Row gutter={8}>
            <Col span={8}>
              <Form.Item name={['position', 'x']} noStyle>
                <InputNumber
                  size="small"
                  step={0.1}
                  placeholder="X"
                  addonBefore="X"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name={['position', 'y']} noStyle>
                <InputNumber
                  size="small"
                  step={0.1}
                  placeholder="Y"
                  addonBefore="Y"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name={['position', 'z']} noStyle>
                <InputNumber
                  size="small"
                  step={0.1}
                  placeholder="Z"
                  addonBefore="Z"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
          </Row>
        </Form.Item>

        <Divider style={{ margin: '8px 0' }} />

        {/* 旋转 */}
        <Form.Item label={t('editor.transform.rotation')} style={{ marginBottom: 12 }}>
          <Row gutter={8}>
            <Col span={8}>
              <Form.Item name={['rotation', 'x']} noStyle>
                <InputNumber
                  size="small"
                  step={1}
                  min={-360}
                  max={360}
                  placeholder="X"
                  addonBefore="X"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name={['rotation', 'y']} noStyle>
                <InputNumber
                  size="small"
                  step={1}
                  min={-360}
                  max={360}
                  placeholder="Y"
                  addonBefore="Y"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name={['rotation', 'z']} noStyle>
                <InputNumber
                  size="small"
                  step={1}
                  min={-360}
                  max={360}
                  placeholder="Z"
                  addonBefore="Z"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
          </Row>
        </Form.Item>

        <Divider style={{ margin: '8px 0' }} />

        {/* 缩放 */}
        <Form.Item
          label={
            <Space>
              <span>{t('editor.transform.scale')}</span>
              {!readonly && (
                <Tooltip title={isLinked ? t('editor.transform.unlinkScale') : t('editor.transform.linkScale')}>
                  <Button
                    type="text"
                    size="small"
                    icon={<LinkOutlined />}
                    onClick={handleToggleLink}
                    style={{
                      color: isLinked ? '#1890ff' : '#999',
                      padding: '0 4px'
                    }}
                  />
                </Tooltip>
              )}
            </Space>
          }
          style={{ marginBottom: 12 }}
        >
          <Row gutter={8}>
            <Col span={8}>
              <Form.Item name={['scale', 'x']} noStyle>
                <InputNumber
                  size="small"
                  step={0.1}
                  min={0.01}
                  placeholder="X"
                  addonBefore="X"
                  style={{ width: '100%' }}
                  onChange={(value) => value && handleScaleChange('x', value)}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name={['scale', 'y']} noStyle>
                <InputNumber
                  size="small"
                  step={0.1}
                  min={0.01}
                  placeholder="Y"
                  addonBefore="Y"
                  style={{ width: '100%' }}
                  onChange={(value) => value && handleScaleChange('y', value)}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name={['scale', 'z']} noStyle>
                <InputNumber
                  size="small"
                  step={0.1}
                  min={0.01}
                  placeholder="Z"
                  addonBefore="Z"
                  style={{ width: '100%' }}
                  onChange={(value) => value && handleScaleChange('z', value)}
                />
              </Form.Item>
            </Col>
          </Row>
        </Form.Item>

        {/* 实体信息 */}
        {currentEntity && (
          <>
            <Divider style={{ margin: '8px 0' }} />
            <div style={{ fontSize: '12px', color: '#666' }}>
              <Text type="secondary">实体: {currentEntity.name || currentEntity.id}</Text>
            </div>
          </>
        )}
      </Form>
    </Card>
  );
};

export default TransformEditor;
