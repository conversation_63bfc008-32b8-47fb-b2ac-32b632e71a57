/**
 * 对象编辑服务
 * 处理3D场景中对象的选择、变换和编辑操作
 */
import * as THREE from 'three';

export enum TransformMode {
  SELECT = 'select',
  TRANSLATE = 'translate',
  ROTATE = 'rotate',
  SCALE = 'scale'
}

export enum TransformSpace {
  LOCAL = 'local',
  WORLD = 'world'
}

export interface SelectionEvent {
  object: THREE.Object3D | null;
  position: THREE.Vector3;
  normal: THREE.Vector3;
}

class ObjectEditService {
  private static instance: ObjectEditService;
  
  private scene: THREE.Scene | null = null;
  private camera: THREE.Camera | null = null;
  private canvas: HTMLCanvasElement | null = null;
  private raycaster: THREE.Raycaster = new THREE.Raycaster();
  private mouse: THREE.Vector2 = new THREE.Vector2();
  
  // 选择状态
  private selectedObject: THREE.Object3D | null = null;
  private selectableObjects: THREE.Object3D[] = [];
  private outlinePass: any = null; // 用于对象高亮
  
  // 变换控制
  private transformMode: TransformMode = TransformMode.TRANSLATE;
  private transformSpace: TransformSpace = TransformSpace.WORLD;
  private transformControls: any = null; // TransformControls
  
  // 事件监听器
  private selectionListeners: ((event: SelectionEvent) => void)[] = [];
  private transformListeners: ((object: THREE.Object3D) => void)[] = [];
  
  // 状态
  private isEnabled = true;
  private isDragging = false;

  public static getInstance(): ObjectEditService {
    if (!ObjectEditService.instance) {
      ObjectEditService.instance = new ObjectEditService();
    }
    return ObjectEditService.instance;
  }

  /**
   * 初始化对象编辑服务
   */
  public initialize(
    scene: THREE.Scene,
    camera: THREE.Camera,
    canvas: HTMLCanvasElement
  ): void {
    this.scene = scene;
    this.camera = camera;
    this.canvas = canvas;
    
    // 设置射线投射器
    this.raycaster.params.Line.threshold = 0.1;
    this.raycaster.params.Points.threshold = 0.1;
    
    // 绑定事件监听器
    this.bindEventListeners();
    
    console.log('对象编辑服务初始化完成');
  }

  /**
   * 绑定事件监听器
   */
  private bindEventListeners(): void {
    if (!this.canvas) return;

    this.canvas.addEventListener('click', this.onCanvasClick.bind(this));
    this.canvas.addEventListener('mousedown', this.onMouseDown.bind(this));
    this.canvas.addEventListener('mousemove', this.onMouseMove.bind(this));
    this.canvas.addEventListener('mouseup', this.onMouseUp.bind(this));
  }

  /**
   * Canvas点击事件
   */
  private onCanvasClick(event: MouseEvent): void {
    if (!this.isEnabled || this.isDragging) return;
    
    this.updateMousePosition(event);
    this.performRaycast();
  }

  /**
   * 鼠标按下事件
   */
  private onMouseDown(event: MouseEvent): void {
    if (!this.isEnabled) return;
    this.isDragging = false;
  }

  /**
   * 鼠标移动事件
   */
  private onMouseMove(event: MouseEvent): void {
    if (!this.isEnabled) return;
    this.isDragging = true;
  }

  /**
   * 鼠标释放事件
   */
  private onMouseUp(event: MouseEvent): void {
    if (!this.isEnabled) return;
    // 重置拖拽状态
    setTimeout(() => {
      this.isDragging = false;
    }, 10);
  }

  /**
   * 更新鼠标位置
   */
  private updateMousePosition(event: MouseEvent): void {
    if (!this.canvas) return;
    
    const rect = this.canvas.getBoundingClientRect();
    this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
    this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
  }

  /**
   * 执行射线投射
   */
  private performRaycast(): void {
    if (!this.scene || !this.camera) return;
    
    this.raycaster.setFromCamera(this.mouse, this.camera);
    
    // 获取可选择的对象
    const objects = this.selectableObjects.length > 0 
      ? this.selectableObjects 
      : this.scene.children.filter(child => child.visible);
    
    const intersects = this.raycaster.intersectObjects(objects, true);
    
    if (intersects.length > 0) {
      const intersection = intersects[0];
      let targetObject = intersection.object;
      
      // 向上查找可选择的父对象
      while (targetObject.parent && !this.isSelectableObject(targetObject)) {
        targetObject = targetObject.parent;
      }
      
      this.selectObject(targetObject, intersection.point, intersection.face?.normal || new THREE.Vector3());
    } else {
      this.selectObject(null, new THREE.Vector3(), new THREE.Vector3());
    }
  }

  /**
   * 检查对象是否可选择
   */
  private isSelectableObject(object: THREE.Object3D): boolean {
    if (this.selectableObjects.length === 0) {
      // 如果没有指定可选择对象，则所有可见对象都可选择
      return object.visible && object.type !== 'Scene';
    }
    return this.selectableObjects.includes(object);
  }

  /**
   * 选择对象
   */
  public selectObject(
    object: THREE.Object3D | null, 
    position: THREE.Vector3 = new THREE.Vector3(),
    normal: THREE.Vector3 = new THREE.Vector3()
  ): void {
    // 取消之前的选择
    if (this.selectedObject) {
      this.unhighlightObject(this.selectedObject);
    }
    
    this.selectedObject = object;
    
    // 高亮新选择的对象
    if (this.selectedObject) {
      this.highlightObject(this.selectedObject);
      console.log('选择对象:', this.selectedObject.name || this.selectedObject.type);
    } else {
      console.log('取消选择');
    }
    
    // 触发选择事件
    const selectionEvent: SelectionEvent = {
      object: this.selectedObject,
      position: position.clone(),
      normal: normal.clone()
    };
    
    this.selectionListeners.forEach(listener => listener(selectionEvent));
  }

  /**
   * 高亮对象
   */
  private highlightObject(object: THREE.Object3D): void {
    // 简单的高亮实现：改变材质颜色
    if (object instanceof THREE.Mesh) {
      const material = object.material;
      if (material instanceof THREE.MeshStandardMaterial) {
        // 保存原始颜色
        if (!object.userData.originalColor) {
          object.userData.originalColor = material.color.clone();
        }
        // 设置高亮颜色
        material.color.setHex(0x00ff00);
      }
    }
    
    // 递归高亮子对象
    object.children.forEach(child => {
      if (child instanceof THREE.Mesh) {
        this.highlightObject(child);
      }
    });
  }

  /**
   * 取消高亮对象
   */
  private unhighlightObject(object: THREE.Object3D): void {
    // 恢复原始材质颜色
    if (object instanceof THREE.Mesh) {
      const material = object.material;
      if (material instanceof THREE.MeshStandardMaterial && object.userData.originalColor) {
        material.color.copy(object.userData.originalColor);
        delete object.userData.originalColor;
      }
    }
    
    // 递归取消高亮子对象
    object.children.forEach(child => {
      if (child instanceof THREE.Mesh) {
        this.unhighlightObject(child);
      }
    });
  }

  /**
   * 设置变换模式
   */
  public setTransformMode(mode: TransformMode): void {
    this.transformMode = mode;
    console.log('变换模式:', mode);
  }

  /**
   * 设置变换空间
   */
  public setTransformSpace(space: TransformSpace): void {
    this.transformSpace = space;
    console.log('变换空间:', space);
  }

  /**
   * 移动选中对象
   */
  public translateObject(delta: THREE.Vector3): void {
    if (!this.selectedObject) return;
    
    this.selectedObject.position.add(delta);
    this.notifyTransformChange();
  }

  /**
   * 旋转选中对象
   */
  public rotateObject(axis: THREE.Vector3, angle: number): void {
    if (!this.selectedObject) return;
    
    this.selectedObject.rotateOnAxis(axis, angle);
    this.notifyTransformChange();
  }

  /**
   * 缩放选中对象
   */
  public scaleObject(scale: THREE.Vector3): void {
    if (!this.selectedObject) return;
    
    this.selectedObject.scale.multiply(scale);
    this.notifyTransformChange();
  }

  /**
   * 通知变换变化
   */
  private notifyTransformChange(): void {
    if (this.selectedObject) {
      this.transformListeners.forEach(listener => listener(this.selectedObject!));
    }
  }

  /**
   * 设置可选择对象列表
   */
  public setSelectableObjects(objects: THREE.Object3D[]): void {
    this.selectableObjects = objects;
  }

  /**
   * 添加选择事件监听器
   */
  public addSelectionListener(listener: (event: SelectionEvent) => void): void {
    this.selectionListeners.push(listener);
  }

  /**
   * 移除选择事件监听器
   */
  public removeSelectionListener(listener: (event: SelectionEvent) => void): void {
    const index = this.selectionListeners.indexOf(listener);
    if (index > -1) {
      this.selectionListeners.splice(index, 1);
    }
  }

  /**
   * 添加变换事件监听器
   */
  public addTransformListener(listener: (object: THREE.Object3D) => void): void {
    this.transformListeners.push(listener);
  }

  /**
   * 移除变换事件监听器
   */
  public removeTransformListener(listener: (object: THREE.Object3D) => void): void {
    const index = this.transformListeners.indexOf(listener);
    if (index > -1) {
      this.transformListeners.splice(index, 1);
    }
  }

  /**
   * 获取选中对象
   */
  public getSelectedObject(): THREE.Object3D | null {
    return this.selectedObject;
  }

  /**
   * 获取变换模式
   */
  public getTransformMode(): TransformMode {
    return this.transformMode;
  }

  /**
   * 获取变换空间
   */
  public getTransformSpace(): TransformSpace {
    return this.transformSpace;
  }

  /**
   * 启用/禁用编辑
   */
  public setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }

  /**
   * 销毁服务
   */
  public dispose(): void {
    // 取消选择
    this.selectObject(null);
    
    // 移除事件监听器
    if (this.canvas) {
      this.canvas.removeEventListener('click', this.onCanvasClick.bind(this));
      this.canvas.removeEventListener('mousedown', this.onMouseDown.bind(this));
      this.canvas.removeEventListener('mousemove', this.onMouseMove.bind(this));
      this.canvas.removeEventListener('mouseup', this.onMouseUp.bind(this));
    }
    
    // 清理引用
    this.scene = null;
    this.camera = null;
    this.canvas = null;
    this.selectedObject = null;
    this.selectableObjects = [];
    this.selectionListeners = [];
    this.transformListeners = [];
    
    console.log('对象编辑服务已销毁');
  }
}

export default ObjectEditService;
