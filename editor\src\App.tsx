/**
 * 应用程序主组件
 */
import React, { useEffect } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider, theme, Spin } from 'antd';
import { useTranslation } from 'react-i18next';

import { useAppDispatch, useAppSelector } from './store';
import { checkAuth } from './store/auth/authSlice';
import { ThemeType } from './store/ui/uiSlice';
import { forceCloseConflictPanel } from './store/collaboration/conflictSlice';
import { AppLayout } from './components/layout/AppLayout';
import { LoginPage } from './pages/LoginPage';
import { RegisterPage } from './pages/RegisterPage';
import { ProjectsPage } from './pages/ProjectsPage';
import { EditorPage } from './pages/EditorPage';
import { NotFoundPage } from './pages/NotFoundPage';
import { I18nTestPage } from './pages/I18nTestPage';
import TerrainEditorPage from './pages/TerrainEditorPage';
import TranslationTestPage from './pages/TranslationTestPage';
import ApiTestPage from './pages/ApiTestPage';
import FeedbackSystemDemo from './pages/FeedbackSystemDemo';
import AchievementNotification from './components/achievements/AchievementNotification';
import TutorialHighlightContainer from './components/tutorials/TutorialHighlight';
import GitPanel from './components/git/GitPanel';
import GitConflictResolver from './components/git/GitConflictResolver';
import GlobalNotifications from './components/common/GlobalNotifications';
import { microserviceIntegration } from './services/MicroserviceIntegration';
import GitService from './services/GitService';
import { config } from './config/environment';
import { DevToolsFloatButton } from './components/debug/DevToolsPanel';

const App: React.FC = () => {
  const dispatch = useAppDispatch();
  const { i18n } = useTranslation();
  const { isAuthenticated, isLoading } = useAppSelector((state) => state.auth);
  const { theme: themeType, language } = useAppSelector((state) => state.ui);

  // 检查认证状态
  useEffect(() => {
    const token = localStorage.getItem('token');

    // 如果有 token：
    // - 初始 isLoading 由 authSlice 根据是否存在 token 决定
    // - 当 isLoading 为 true 时，应尽快触发一次 checkAuth
    if (token && !isAuthenticated) {
      console.log('检查认证状态，token:', token);
      dispatch(checkAuth());
    }

    // 确保冲突面板在启动时是关闭的
    dispatch(forceCloseConflictPanel());
  }, [dispatch, isAuthenticated]);

  // 设置语言
  useEffect(() => {
    if (language && i18n.isInitialized) {
      i18n.changeLanguage(language);
    }
  }, [language, i18n]);

  // 初始化微服务集成和Git服务（只在用户登录后）
  useEffect(() => {
    const initServices = async () => {
      try {
        if (config.enableDebug) {
          console.log('🚀 初始化服务...');
        }

        // 暂时跳过微服务集成初始化，避免阻塞应用
        // await microserviceIntegration.initialize();

        // 暂时跳过Git服务启动
        // GitService.start();

        if (config.enableDebug) {
          console.log('✅ 服务初始化完成');
        }
      } catch (error) {
        console.error('❌ 服务初始化失败:', error);
      }
    };

    // 只在用户已认证时才初始化服务
    if (isAuthenticated) {
      initServices();
    }

    // 清理函数
    return () => {
      if (isAuthenticated) {
        // microserviceIntegration.destroy();
        // GitService.destroy();
      }
    };
  }, [isAuthenticated]);

  // 如果正在加载认证状态，显示加载中
  if (isLoading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <Spin size="large" tip={t('common.loading')} />
      </div>
    );
  }

  // 调试信息
  console.log('App渲染状态:', {
    isAuthenticated,
    isLoading,
    token: localStorage.getItem('token'),
    currentPath: window.location.pathname,
    userAgent: navigator.userAgent
  });

  return (
    <ConfigProvider
      theme={{
        algorithm: themeType === ThemeType.DARK ? theme.darkAlgorithm : theme.defaultAlgorithm}}
    >
      <>
        <Routes>
          {/* 公共路由 */}
          <Route
            path="/login"
            element={
              isLoading ? (
                <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
                  <Spin size="large" tip={t('auth.verifyingLogin')} />
                </div>
              ) : (
                <LoginPage />
              )
            }
          />
          <Route
            path="/register"
            element={
              isLoading ? (
                <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
                  <Spin size="large" tip={t('auth.verifyingLogin')} />
                </div>
              ) : (
                <RegisterPage />
              )
            }
          />
          <Route path="/i18n-test" element={<I18nTestPage />} />
          <Route path="/translation-test" element={<TranslationTestPage />} />
          <Route path="/api-test" element={<ApiTestPage />} />
          <Route path="/debug" element={
            <div style={{ padding: '20px' }}>
              <h1>调试页面</h1>
              <p>认证状态: {isAuthenticated ? '已认证' : '未认证'}</p>
              <p>加载状态: {isLoading ? '加载中' : '已完成'}</p>
              <p>Token: {localStorage.getItem('token') || '无'}</p>
              <p>当前路径: {window.location.pathname}</p>
            </div>
          } />

          {/* 编辑器路由（独立，不需要AppLayout包装） */}
          <Route
            path="/editor/:projectId/:sceneId"
            element={
              isLoading ? (
                <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
                  <Spin size="large" tip={t('common.loading')} />
                </div>
              ) : isAuthenticated ? (
                <EditorPage />
              ) : (
                <Navigate to="/login" replace />
              )
            }
          />

          {/* 编辑器全屏模式 */}
          <Route path="/editor/:projectId/:sceneId/fullscreen" element={<EditorPage />} />

          {/* 需要认证的路由 */}
          <Route
            path="/"
            element={
              isLoading ? (
                <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
                  <Spin size="large" tip={t('common.loading')} />
                </div>
              ) : isAuthenticated ? (
                <AppLayout />
              ) : (
                <Navigate to="/login" replace />
              )
            }
          >
            <Route index element={<Navigate to="/projects" replace />} />
            <Route path="projects" element={<ProjectsPage />} />
            <Route path="feedback-demo" element={<FeedbackSystemDemo />} />
          </Route>

          {/* 404页面 */}
          <Route path="*" element={<NotFoundPage />} />
        </Routes>

        {/* 全局组件 */}
        <GlobalNotifications />
        {isAuthenticated && (
          <>
            <AchievementNotification />
            <TutorialHighlightContainer />
            <GitPanel />
            <GitConflictResolver />
          </>
        )}

        {/* 开发工具 */}
        <DevToolsFloatButton />
      </>
    </ConfigProvider>
  );
};

export default App;
